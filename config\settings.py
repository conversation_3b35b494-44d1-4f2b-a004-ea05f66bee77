import os
from config.database import db_manager

class Settings:
    def __init__(self):
        self.load_settings()
    
    def load_settings(self):
        """تحميل الإعدادات من قاعدة البيانات"""
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("SELECT key, value FROM settings")
        settings_data = cursor.fetchall()
        
        # الإعدادات الافتراضية
        self.settings = {
            'company_name': 'شركة إدارة المخازن',
            'currency': 'ريال',
            'language': 'ar',
            'low_stock_alert': 10,
            'backup_interval': 7,
            'theme': 'dark',
            'font_size': 12,
            'window_width': 1200,
            'window_height': 800
        }
        
        # تحديث الإعدادات من قاعدة البيانات
        for row in settings_data:
            key, value = row['key'], row['value']
            if key in ['low_stock_alert', 'backup_interval', 'font_size', 'window_width', 'window_height']:
                self.settings[key] = int(value)
            else:
                self.settings[key] = value
        
        conn.close()
    
    def get(self, key, default=None):
        """الحصول على قيمة إعداد"""
        return self.settings.get(key, default)
    
    def set(self, key, value):
        """تعيين قيمة إعداد"""
        self.settings[key] = value
        self.save_setting(key, value)
    
    def save_setting(self, key, value):
        """حفظ إعداد في قاعدة البيانات"""
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT OR REPLACE INTO settings (key, value)
            VALUES (?, ?)
        ''', (key, str(value)))
        
        conn.commit()
        conn.close()
    
    def get_all(self):
        """الحصول على جميع الإعدادات"""
        return self.settings.copy()

# إنشاء مثيل واحد من الإعدادات
app_settings = Settings()
