import customtkinter as ctk
import tkinter as tk
from tkinter import ttk, messagebox

from models.user import User
from utils.validators import UserValidator

class UsersView:
    def __init__(self, parent, main_window):
        self.parent = parent
        self.main_window = main_window
        self.current_user = main_window.current_user
        self.selected_user = None
        self.users_data = []
        
        # التحقق من الصلاحيات
        if not self.current_user.has_permission('all'):
            messagebox.showerror("خطأ", "ليس لديك صلاحية للوصول إلى هذه الصفحة")
            return
        
        self.setup_ui()
        self.load_users()
    
    def setup_ui(self):
        """إعداد واجهة إدارة المستخدمين"""
        # إطار رئيسي
        self.main_frame = ctk.CTkFrame(self.parent)
        self.main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # عنوان
        title = ctk.CTkLabel(
            self.main_frame,
            text="إدارة المستخدمين",
            font=("Arial", 20, "bold")
        )
        title.pack(pady=10)
        
        # إطار الأزرار العلوية
        buttons_frame = ctk.CTkFrame(self.main_frame)
        buttons_frame.pack(fill="x", padx=10, pady=5)
        
        # أزرار التحكم
        add_btn = ctk.CTkButton(
            buttons_frame,
            text="➕ إضافة مستخدم",
            command=self.add_user,
            width=120
        )
        add_btn.pack(side="right", padx=5, pady=10)
        
        edit_btn = ctk.CTkButton(
            buttons_frame,
            text="✏️ تعديل",
            command=self.edit_user,
            width=120
        )
        edit_btn.pack(side="right", padx=5, pady=10)
        
        delete_btn = ctk.CTkButton(
            buttons_frame,
            text="🗑️ حذف",
            command=self.delete_user,
            width=120,
            fg_color="red"
        )
        delete_btn.pack(side="right", padx=5, pady=10)
        
        reset_password_btn = ctk.CTkButton(
            buttons_frame,
            text="🔑 إعادة تعيين كلمة المرور",
            command=self.reset_password,
            width=180,
            fg_color="orange"
        )
        reset_password_btn.pack(side="left", padx=5, pady=10)
        
        # إطار المحتوى الرئيسي
        content_frame = ctk.CTkFrame(self.main_frame)
        content_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        # جدول المستخدمين
        self.create_users_table(content_frame)
    
    def create_users_table(self, parent):
        """إنشاء جدول المستخدمين"""
        # إطار الجدول
        table_frame = ctk.CTkFrame(parent)
        table_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # أعمدة الجدول
        columns = ("اسم المستخدم", "الاسم الكامل", "الدور", "الحالة", "تاريخ الإنشاء")
        
        self.users_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)
        
        # تعيين عناوين الأعمدة وعرضها
        column_widths = [150, 200, 120, 100, 150]
        for i, col in enumerate(columns):
            self.users_tree.heading(col, text=col)
            self.users_tree.column(col, width=column_widths[i], anchor="center")
        
        # شريط التمرير
        scrollbar_y = ttk.Scrollbar(table_frame, orient="vertical", command=self.users_tree.yview)
        scrollbar_x = ttk.Scrollbar(table_frame, orient="horizontal", command=self.users_tree.xview)
        
        self.users_tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
        
        # تخطيط الجدول
        self.users_tree.pack(side="right", fill="both", expand=True)
        scrollbar_y.pack(side="left", fill="y")
        scrollbar_x.pack(side="bottom", fill="x")
        
        # ربط النقر المزدوج بالتعديل
        self.users_tree.bind("<Double-1>", lambda e: self.edit_user())
    
    def load_users(self):
        """تحميل المستخدمين في الجدول"""
        # مسح البيانات الحالية
        for item in self.users_tree.get_children():
            self.users_tree.delete(item)
        
        # تحميل المستخدمين
        self.users_data = User.get_all(active_only=False)
        
        for user in self.users_data:
            status = "نشط" if user.is_active else "غير نشط"
            
            # تحويل الدور إلى العربية
            role_ar = {
                'admin': 'مدير',
                'manager': 'مشرف',
                'employee': 'موظف'
            }.get(user.role, user.role)
            
            self.users_tree.insert("", "end", values=(
                user.username,
                user.full_name,
                role_ar,
                status,
                "غير محدد"  # يمكن إضافة تاريخ الإنشاء لاحقاً
            ))
    
    def get_selected_user(self):
        """الحصول على المستخدم المحدد"""
        selection = self.users_tree.selection()
        if not selection:
            return None
        
        item = self.users_tree.item(selection[0])
        username = item['values'][0]
        
        # البحث عن المستخدم في البيانات المحملة
        for user in self.users_data:
            if user.username == username:
                return user
        
        return None
    
    def add_user(self):
        """إضافة مستخدم جديد"""
        self.selected_user = None
        self.show_user_form()
    
    def edit_user(self):
        """تعديل مستخدم موجود"""
        selected_user = self.get_selected_user()
        if not selected_user:
            messagebox.showwarning("تحذير", "يرجى اختيار مستخدم للتعديل")
            return
        
        # منع تعديل المدير الرئيسي
        if selected_user.username == "admin" and selected_user.id == 1:
            messagebox.showwarning("تحذير", "لا يمكن تعديل المدير الرئيسي")
            return
        
        self.selected_user = selected_user
        self.show_user_form()
    
    def delete_user(self):
        """حذف مستخدم"""
        selected_user = self.get_selected_user()
        if not selected_user:
            messagebox.showwarning("تحذير", "يرجى اختيار مستخدم للحذف")
            return
        
        # منع حذف المدير الرئيسي
        if selected_user.username == "admin" and selected_user.id == 1:
            messagebox.showwarning("تحذير", "لا يمكن حذف المدير الرئيسي")
            return
        
        # منع حذف المستخدم الحالي
        if selected_user.id == self.current_user.id:
            messagebox.showwarning("تحذير", "لا يمكن حذف المستخدم الحالي")
            return
        
        result = messagebox.askyesno(
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف المستخدم '{selected_user.full_name}'؟"
        )
        
        if result:
            selected_user.delete()
            self.load_users()
            messagebox.showinfo("نجح", "تم حذف المستخدم بنجاح")
    
    def reset_password(self):
        """إعادة تعيين كلمة المرور"""
        selected_user = self.get_selected_user()
        if not selected_user:
            messagebox.showwarning("تحذير", "يرجى اختيار مستخدم لإعادة تعيين كلمة المرور")
            return
        
        # نافذة إدخال كلمة المرور الجديدة
        password_window = ctk.CTkToplevel(self.parent)
        password_window.title("إعادة تعيين كلمة المرور")
        password_window.geometry("400x200")
        password_window.transient(self.parent)
        password_window.grab_set()
        
        # إطار رئيسي
        main_frame = ctk.CTkFrame(password_window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # عنوان
        title = ctk.CTkLabel(
            main_frame,
            text=f"إعادة تعيين كلمة المرور للمستخدم: {selected_user.full_name}",
            font=("Arial", 14, "bold")
        )
        title.pack(pady=10)
        
        # كلمة المرور الجديدة
        password_frame = ctk.CTkFrame(main_frame)
        password_frame.pack(fill="x", pady=10)
        
        ctk.CTkLabel(password_frame, text="كلمة المرور الجديدة:", width=150).pack(side="right", padx=5)
        password_entry = ctk.CTkEntry(password_frame, show="*", width=200)
        password_entry.pack(side="left", padx=5)
        
        # تأكيد كلمة المرور
        confirm_frame = ctk.CTkFrame(main_frame)
        confirm_frame.pack(fill="x", pady=5)
        
        ctk.CTkLabel(confirm_frame, text="تأكيد كلمة المرور:", width=150).pack(side="right", padx=5)
        confirm_entry = ctk.CTkEntry(confirm_frame, show="*", width=200)
        confirm_entry.pack(side="left", padx=5)
        
        # أزرار
        buttons_frame = ctk.CTkFrame(main_frame)
        buttons_frame.pack(fill="x", pady=20)
        
        def save_password():
            password = password_entry.get().strip()
            confirm = confirm_entry.get().strip()
            
            if not password:
                messagebox.showerror("خطأ", "يرجى إدخال كلمة المرور")
                return
            
            if password != confirm:
                messagebox.showerror("خطأ", "كلمة المرور وتأكيدها غير متطابقتين")
                return
            
            if len(password) < 6:
                messagebox.showerror("خطأ", "كلمة المرور يجب أن تكون 6 أحرف على الأقل")
                return
            
            # تحديث كلمة المرور
            selected_user.password = password
            selected_user.save()
            
            password_window.destroy()
            messagebox.showinfo("نجح", "تم تحديث كلمة المرور بنجاح")
        
        save_btn = ctk.CTkButton(
            buttons_frame,
            text="💾 حفظ",
            command=save_password,
            width=120
        )
        save_btn.pack(side="left", padx=10)
        
        cancel_btn = ctk.CTkButton(
            buttons_frame,
            text="❌ إلغاء",
            command=password_window.destroy,
            width=120,
            fg_color="gray"
        )
        cancel_btn.pack(side="right", padx=10)
    
    def show_user_form(self):
        """عرض نموذج إضافة/تعديل المستخدم"""
        # إنشاء نافذة جديدة
        form_window = ctk.CTkToplevel(self.parent)
        form_window.title("إضافة مستخدم" if not self.selected_user else "تعديل مستخدم")
        form_window.geometry("500x500")
        form_window.transient(self.parent)
        form_window.grab_set()
        
        # إطار رئيسي
        main_frame = ctk.CTkScrollableFrame(form_window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # عنوان النموذج
        title = ctk.CTkLabel(
            main_frame,
            text="إضافة مستخدم جديد" if not self.selected_user else "تعديل المستخدم",
            font=("Arial", 18, "bold")
        )
        title.pack(pady=10)
        
        # حقول النموذج
        fields = {}
        
        # اسم المستخدم
        username_frame = ctk.CTkFrame(main_frame)
        username_frame.pack(fill="x", pady=5)
        
        ctk.CTkLabel(username_frame, text="اسم المستخدم *", width=120).pack(side="right", padx=5)
        fields['username'] = ctk.CTkEntry(username_frame, width=300)
        fields['username'].pack(side="left", padx=5)
        
        # الاسم الكامل
        fullname_frame = ctk.CTkFrame(main_frame)
        fullname_frame.pack(fill="x", pady=5)
        
        ctk.CTkLabel(fullname_frame, text="الاسم الكامل *", width=120).pack(side="right", padx=5)
        fields['full_name'] = ctk.CTkEntry(fullname_frame, width=300)
        fields['full_name'].pack(side="left", padx=5)
        
        # كلمة المرور (للمستخدمين الجدد فقط)
        if not self.selected_user:
            password_frame = ctk.CTkFrame(main_frame)
            password_frame.pack(fill="x", pady=5)
            
            ctk.CTkLabel(password_frame, text="كلمة المرور *", width=120).pack(side="right", padx=5)
            fields['password'] = ctk.CTkEntry(password_frame, show="*", width=300)
            fields['password'].pack(side="left", padx=5)
            
            # تأكيد كلمة المرور
            confirm_frame = ctk.CTkFrame(main_frame)
            confirm_frame.pack(fill="x", pady=5)
            
            ctk.CTkLabel(confirm_frame, text="تأكيد كلمة المرور *", width=120).pack(side="right", padx=5)
            fields['confirm_password'] = ctk.CTkEntry(confirm_frame, show="*", width=300)
            fields['confirm_password'].pack(side="left", padx=5)
        
        # الدور
        role_frame = ctk.CTkFrame(main_frame)
        role_frame.pack(fill="x", pady=5)
        
        ctk.CTkLabel(role_frame, text="الدور *", width=120).pack(side="right", padx=5)
        
        role_options = ["مدير", "مشرف", "موظف"]
        fields['role'] = ctk.CTkComboBox(role_frame, values=role_options, width=300)
        fields['role'].pack(side="left", padx=5)
        
        # الحالة
        status_frame = ctk.CTkFrame(main_frame)
        status_frame.pack(fill="x", pady=5)
        
        ctk.CTkLabel(status_frame, text="الحالة", width=120).pack(side="right", padx=5)
        fields['is_active'] = ctk.CTkCheckBox(status_frame, text="نشط")
        fields['is_active'].pack(side="left", padx=5)
        
        # ملء البيانات في حالة التعديل
        if self.selected_user:
            self.fill_user_form(fields)
        else:
            # قيم افتراضية للمستخدم الجديد
            fields['role'].set("موظف")
            fields['is_active'].select()
        
        # أزرار الحفظ والإلغاء
        buttons_frame = ctk.CTkFrame(main_frame)
        buttons_frame.pack(fill="x", pady=20)
        
        save_btn = ctk.CTkButton(
            buttons_frame,
            text="💾 حفظ",
            command=lambda: self.save_user(fields, form_window),
            width=120
        )
        save_btn.pack(side="left", padx=10)
        
        cancel_btn = ctk.CTkButton(
            buttons_frame,
            text="❌ إلغاء",
            command=form_window.destroy,
            width=120,
            fg_color="gray"
        )
        cancel_btn.pack(side="right", padx=10)
    
    def fill_user_form(self, fields):
        """ملء نموذج المستخدم بالبيانات الحالية"""
        if not self.selected_user:
            return
        
        fields['username'].insert(0, self.selected_user.username or "")
        fields['full_name'].insert(0, self.selected_user.full_name or "")
        
        # تعيين الدور
        role_mapping = {
            'admin': 'مدير',
            'manager': 'مشرف',
            'employee': 'موظف'
        }
        role_ar = role_mapping.get(self.selected_user.role, 'موظف')
        fields['role'].set(role_ar)
        
        if self.selected_user.is_active:
            fields['is_active'].select()
    
    def save_user(self, fields, form_window):
        """حفظ المستخدم"""
        try:
            # جمع البيانات من النموذج
            data = {
                'username': fields['username'].get().strip(),
                'full_name': fields['full_name'].get().strip(),
                'role': fields['role'].get().strip(),
                'is_active': fields['is_active'].get(),
                'is_new_user': not self.selected_user
            }
            
            # إضافة كلمة المرور للمستخدمين الجدد
            if not self.selected_user:
                password = fields['password'].get().strip()
                confirm_password = fields['confirm_password'].get().strip()
                
                if password != confirm_password:
                    messagebox.showerror("خطأ", "كلمة المرور وتأكيدها غير متطابقتين")
                    return
                
                data['password'] = password
            
            # تحويل الدور إلى الإنجليزية
            role_mapping = {
                'مدير': 'admin',
                'مشرف': 'manager',
                'موظف': 'employee'
            }
            data['role'] = role_mapping.get(data['role'], 'employee')
            
            # التحقق من صحة البيانات
            errors = UserValidator.validate_user_data(data)
            if errors:
                messagebox.showerror("خطأ في البيانات", "\n".join(errors))
                return
            
            # إنشاء أو تحديث المستخدم
            if self.selected_user:
                # تحديث مستخدم موجود
                self.selected_user.username = data['username']
                self.selected_user.full_name = data['full_name']
                self.selected_user.role = data['role']
                self.selected_user.is_active = data['is_active']
                
                self.selected_user.save()
                messagebox.showinfo("نجح", "تم تحديث المستخدم بنجاح")
            else:
                # إضافة مستخدم جديد
                new_user = User(
                    username=data['username'],
                    password=data['password'],
                    full_name=data['full_name'],
                    role=data['role'],
                    is_active=data['is_active']
                )
                
                new_user.save()
                messagebox.showinfo("نجح", "تم إضافة المستخدم بنجاح")
            
            # إغلاق النموذج وتحديث الجدول
            form_window.destroy()
            self.load_users()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ المستخدم:\n{str(e)}")
