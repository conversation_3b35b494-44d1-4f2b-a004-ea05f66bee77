import customtkinter as ctk
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib.dates as mdates

from models.product import Product
from models.transaction import InventoryTransaction
from models.supplier import Supplier

class ReportsView:
    def __init__(self, parent, main_window):
        self.parent = parent
        self.main_window = main_window
        self.current_user = main_window.current_user
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة التقارير"""
        # إطار رئيسي
        self.main_frame = ctk.CTkFrame(self.parent)
        self.main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # عنوان
        title = ctk.CTkLabel(
            self.main_frame,
            text="التقارير والإحصائيات",
            font=("Arial", 20, "bold")
        )
        title.pack(pady=10)
        
        # إطار أنواع التقارير
        reports_frame = ctk.CTkFrame(self.main_frame)
        reports_frame.pack(fill="x", padx=10, pady=5)
        
        # أزرار التقارير
        inventory_report_btn = ctk.CTkButton(
            reports_frame,
            text="📊 تقرير المخزون",
            command=self.show_inventory_report,
            width=150,
            height=40
        )
        inventory_report_btn.pack(side="right", padx=5, pady=10)
        
        transactions_report_btn = ctk.CTkButton(
            reports_frame,
            text="🔄 تقرير الحركات",
            command=self.show_transactions_report,
            width=150,
            height=40
        )
        transactions_report_btn.pack(side="right", padx=5, pady=10)
        
        suppliers_report_btn = ctk.CTkButton(
            reports_frame,
            text="🏢 تقرير الموردين",
            command=self.show_suppliers_report,
            width=150,
            height=40
        )
        suppliers_report_btn.pack(side="right", padx=5, pady=10)
        
        low_stock_btn = ctk.CTkButton(
            reports_frame,
            text="⚠️ المخزون المنخفض",
            command=self.show_low_stock_report,
            width=150,
            height=40,
            fg_color="orange"
        )
        low_stock_btn.pack(side="left", padx=5, pady=10)
        
        # إطار المحتوى
        self.content_frame = ctk.CTkFrame(self.main_frame)
        self.content_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        # عرض لوحة المعلومات الافتراضية
        self.show_dashboard()
    
    def clear_content(self):
        """مسح المحتوى الحالي"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()
    
    def show_dashboard(self):
        """عرض لوحة المعلومات الرئيسية"""
        self.clear_content()
        
        # عنوان
        title = ctk.CTkLabel(
            self.content_frame,
            text="لوحة المعلومات الإحصائية",
            font=("Arial", 16, "bold")
        )
        title.pack(pady=10)
        
        # إطار الإحصائيات السريعة
        stats_frame = ctk.CTkFrame(self.content_frame)
        stats_frame.pack(fill="x", padx=20, pady=10)
        
        # حساب الإحصائيات
        products = Product.get_all()
        low_stock_products = Product.get_low_stock_products()
        recent_transactions = InventoryTransaction.get_all(limit=30)
        suppliers = Supplier.get_all()
        
        # بطاقات الإحصائيات
        self.create_stat_card(stats_frame, "📦", "إجمالي المنتجات", str(len(products)))
        self.create_stat_card(stats_frame, "⚠️", "مخزون منخفض", str(len(low_stock_products)))
        self.create_stat_card(stats_frame, "🔄", "المعاملات (30 يوم)", str(len(recent_transactions)))
        self.create_stat_card(stats_frame, "🏢", "الموردين", str(len(suppliers)))
        
        # إطار الرسوم البيانية
        charts_frame = ctk.CTkFrame(self.content_frame)
        charts_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        try:
            # رسم بياني للمعاملات الأخيرة
            self.create_transactions_chart(charts_frame)
        except Exception as e:
            error_label = ctk.CTkLabel(
                charts_frame,
                text=f"خطأ في عرض الرسم البياني: {str(e)}",
                text_color="red"
            )
            error_label.pack(pady=20)
    
    def create_stat_card(self, parent, icon, title, value):
        """إنشاء بطاقة إحصائية"""
        card = ctk.CTkFrame(parent, width=150, height=100)
        card.pack_propagate(False)
        card.pack(side="right", padx=10, pady=10)
        
        icon_label = ctk.CTkLabel(card, text=icon, font=("Arial", 24))
        icon_label.pack(pady=(10, 5))
        
        value_label = ctk.CTkLabel(card, text=value, font=("Arial", 18, "bold"))
        value_label.pack()
        
        title_label = ctk.CTkLabel(card, text=title, font=("Arial", 10))
        title_label.pack(pady=(0, 10))
    
    def create_transactions_chart(self, parent):
        """إنشاء رسم بياني للمعاملات"""
        try:
            # الحصول على المعاملات للأيام الـ 30 الماضية
            end_date = datetime.now()
            start_date = end_date - timedelta(days=30)
            
            transactions = InventoryTransaction.get_by_date_range(
                start_date.strftime("%Y-%m-%d"),
                end_date.strftime("%Y-%m-%d")
            )
            
            # تجميع البيانات حسب التاريخ
            daily_data = {}
            for transaction in transactions:
                date_key = transaction.transaction_date.strftime("%Y-%m-%d")
                if date_key not in daily_data:
                    daily_data[date_key] = {'in': 0, 'out': 0}
                
                if transaction.transaction_type == 'in':
                    daily_data[date_key]['in'] += transaction.quantity
                elif transaction.transaction_type == 'out':
                    daily_data[date_key]['out'] += transaction.quantity
            
            # إنشاء الرسم البياني
            fig, ax = plt.subplots(figsize=(10, 4))
            
            dates = list(daily_data.keys())
            in_values = [daily_data[date]['in'] for date in dates]
            out_values = [daily_data[date]['out'] for date in dates]
            
            if dates:
                ax.plot(dates, in_values, label='وارد', color='green', marker='o')
                ax.plot(dates, out_values, label='صادر', color='red', marker='s')
                
                ax.set_title('حركة المخزون - آخر 30 يوم')
                ax.set_xlabel('التاريخ')
                ax.set_ylabel('الكمية')
                ax.legend()
                ax.grid(True, alpha=0.3)
                
                # تنسيق التواريخ
                if len(dates) > 10:
                    ax.tick_params(axis='x', rotation=45)
            else:
                ax.text(0.5, 0.5, 'لا توجد بيانات للعرض', 
                       horizontalalignment='center', verticalalignment='center',
                       transform=ax.transAxes, fontsize=14)
            
            plt.tight_layout()
            
            # إضافة الرسم البياني إلى الواجهة
            canvas = FigureCanvasTkAgg(fig, parent)
            canvas.draw()
            canvas.get_tk_widget().pack(fill="both", expand=True, padx=10, pady=10)
            
        except ImportError:
            error_label = ctk.CTkLabel(
                parent,
                text="مكتبة matplotlib غير مثبتة\nيرجى تثبيتها لعرض الرسوم البيانية",
                text_color="orange"
            )
            error_label.pack(pady=20)
        except Exception as e:
            error_label = ctk.CTkLabel(
                parent,
                text=f"خطأ في إنشاء الرسم البياني: {str(e)}",
                text_color="red"
            )
            error_label.pack(pady=20)
    
    def show_inventory_report(self):
        """عرض تقرير المخزون"""
        self.clear_content()
        
        # عنوان
        title = ctk.CTkLabel(
            self.content_frame,
            text="تقرير المخزون الحالي",
            font=("Arial", 16, "bold")
        )
        title.pack(pady=10)
        
        # أزرار التحكم
        buttons_frame = ctk.CTkFrame(self.content_frame)
        buttons_frame.pack(fill="x", padx=20, pady=5)
        
        export_btn = ctk.CTkButton(
            buttons_frame,
            text="📊 تصدير إلى Excel",
            command=self.export_inventory_report,
            width=150
        )
        export_btn.pack(side="left", padx=5, pady=5)
        
        # جدول المخزون
        table_frame = ctk.CTkFrame(self.content_frame)
        table_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        columns = ("الكود", "اسم المنتج", "التصنيف", "الكمية الحالية", "الحد الأدنى", 
                  "قيمة المخزون", "حالة المخزون")
        
        tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)
        
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=120, anchor="center")
        
        # تحميل البيانات
        products = Product.get_all()
        total_value = 0
        
        for product in products:
            # تحديد حالة المخزون
            if product.current_quantity == 0:
                status = "نفد المخزون"
            elif product.current_quantity <= product.min_quantity:
                status = "مخزون منخفض"
            else:
                status = "مخزون عادي"
            
            value = product.current_quantity * product.cost_price
            total_value += value
            
            tree.insert("", "end", values=(
                product.code,
                product.name,
                getattr(product, 'category_name', '') or '',
                product.current_quantity,
                product.min_quantity,
                f"{value:.2f}",
                status
            ))
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)
        
        tree.pack(side="right", fill="both", expand=True)
        scrollbar.pack(side="left", fill="y")
        
        # إجمالي القيمة
        total_frame = ctk.CTkFrame(self.content_frame)
        total_frame.pack(fill="x", padx=20, pady=5)
        
        total_label = ctk.CTkLabel(
            total_frame,
            text=f"إجمالي قيمة المخزون: {total_value:,.2f} ريال",
            font=("Arial", 14, "bold")
        )
        total_label.pack(pady=10)
    
    def show_transactions_report(self):
        """عرض تقرير الحركات"""
        self.clear_content()
        
        # عنوان
        title = ctk.CTkLabel(
            self.content_frame,
            text="تقرير حركات المخزون",
            font=("Arial", 16, "bold")
        )
        title.pack(pady=10)
        
        # فلاتر التقرير
        filters_frame = ctk.CTkFrame(self.content_frame)
        filters_frame.pack(fill="x", padx=20, pady=5)
        
        # فلتر الفترة
        period_frame = ctk.CTkFrame(filters_frame)
        period_frame.pack(side="right", padx=5, pady=5)
        
        ctk.CTkLabel(period_frame, text="الفترة:").pack(side="right", padx=5)
        
        self.report_period_var = ctk.StringVar(value="آخر 30 يوم")
        period_options = ["آخر 7 أيام", "آخر 30 يوم", "آخر 3 أشهر", "آخر سنة"]
        period_combo = ctk.CTkComboBox(
            period_frame,
            values=period_options,
            variable=self.report_period_var,
            command=self.update_transactions_report,
            width=120
        )
        period_combo.pack(side="right", padx=5)
        
        # زر التصدير
        export_btn = ctk.CTkButton(
            filters_frame,
            text="📊 تصدير",
            command=self.export_transactions_report,
            width=100
        )
        export_btn.pack(side="left", padx=5, pady=5)
        
        # إطار النتائج
        self.transactions_report_frame = ctk.CTkFrame(self.content_frame)
        self.transactions_report_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # تحديث التقرير
        self.update_transactions_report()
    
    def update_transactions_report(self, selected_value=None):
        """تحديث تقرير الحركات"""
        # مسح المحتوى الحالي
        for widget in self.transactions_report_frame.winfo_children():
            widget.destroy()
        
        # تحديد الفترة
        period = self.report_period_var.get()
        end_date = datetime.now()
        
        if period == "آخر 7 أيام":
            start_date = end_date - timedelta(days=7)
        elif period == "آخر 30 يوم":
            start_date = end_date - timedelta(days=30)
        elif period == "آخر 3 أشهر":
            start_date = end_date - timedelta(days=90)
        else:  # آخر سنة
            start_date = end_date - timedelta(days=365)
        
        # الحصول على المعاملات
        transactions = InventoryTransaction.get_by_date_range(
            start_date.strftime("%Y-%m-%d"),
            end_date.strftime("%Y-%m-%d")
        )
        
        # إحصائيات سريعة
        stats_frame = ctk.CTkFrame(self.transactions_report_frame)
        stats_frame.pack(fill="x", pady=10)
        
        in_count = len([t for t in transactions if t.transaction_type == 'in'])
        out_count = len([t for t in transactions if t.transaction_type == 'out'])
        total_value = sum(t.total_amount for t in transactions)
        
        ctk.CTkLabel(stats_frame, text=f"إجمالي المعاملات: {len(transactions)}").pack(side="right", padx=10, pady=5)
        ctk.CTkLabel(stats_frame, text=f"معاملات وارد: {in_count}", text_color="green").pack(side="right", padx=10, pady=5)
        ctk.CTkLabel(stats_frame, text=f"معاملات صادر: {out_count}", text_color="red").pack(side="right", padx=10, pady=5)
        ctk.CTkLabel(stats_frame, text=f"إجمالي القيمة: {total_value:,.2f} ريال").pack(side="left", padx=10, pady=5)
    
    def show_suppliers_report(self):
        """عرض تقرير الموردين"""
        self.clear_content()
        
        # عنوان
        title = ctk.CTkLabel(
            self.content_frame,
            text="تقرير الموردين",
            font=("Arial", 16, "bold")
        )
        title.pack(pady=10)
        
        # جدول الموردين
        table_frame = ctk.CTkFrame(self.content_frame)
        table_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        columns = ("اسم المورد", "الشخص المسؤول", "الهاتف", "البريد الإلكتروني", "عدد المعاملات", "الحالة")
        
        tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)
        
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=150, anchor="center")
        
        # تحميل البيانات
        suppliers = Supplier.get_all()
        all_transactions = InventoryTransaction.get_all()
        
        for supplier in suppliers:
            # حساب عدد المعاملات
            supplier_transactions = [t for t in all_transactions if t.supplier_id == supplier.id]
            
            status = "نشط" if supplier.is_active else "غير نشط"
            
            tree.insert("", "end", values=(
                supplier.name,
                supplier.contact_person or "",
                supplier.phone or "",
                supplier.email or "",
                len(supplier_transactions),
                status
            ))
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)
        
        tree.pack(side="right", fill="both", expand=True)
        scrollbar.pack(side="left", fill="y")
    
    def show_low_stock_report(self):
        """عرض تقرير المخزون المنخفض"""
        self.clear_content()
        
        # عنوان
        title = ctk.CTkLabel(
            self.content_frame,
            text="تقرير المخزون المنخفض",
            font=("Arial", 16, "bold"),
            text_color="orange"
        )
        title.pack(pady=10)
        
        # جدول المنتجات منخفضة المخزون
        table_frame = ctk.CTkFrame(self.content_frame)
        table_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        columns = ("الكود", "اسم المنتج", "الكمية الحالية", "الحد الأدنى", "النقص", "الأولوية")
        
        tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)
        
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=120, anchor="center")
        
        # تحميل المنتجات منخفضة المخزون
        low_stock_products = Product.get_low_stock_products()
        
        for product in low_stock_products:
            shortage = product.min_quantity - product.current_quantity
            
            # تحديد الأولوية
            if product.current_quantity == 0:
                priority = "عاجل جداً"
            elif shortage > product.min_quantity * 0.5:
                priority = "عاجل"
            else:
                priority = "متوسط"
            
            tree.insert("", "end", values=(
                product.code,
                product.name,
                product.current_quantity,
                product.min_quantity,
                shortage,
                priority
            ))
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)
        
        tree.pack(side="right", fill="both", expand=True)
        scrollbar.pack(side="left", fill="y")
        
        # إحصائيات
        stats_frame = ctk.CTkFrame(self.content_frame)
        stats_frame.pack(fill="x", padx=20, pady=5)
        
        total_products = len(Product.get_all())
        low_stock_count = len(low_stock_products)
        percentage = (low_stock_count / total_products * 100) if total_products > 0 else 0
        
        stats_label = ctk.CTkLabel(
            stats_frame,
            text=f"المنتجات منخفضة المخزون: {low_stock_count} من {total_products} ({percentage:.1f}%)",
            font=("Arial", 12, "bold")
        )
        stats_label.pack(pady=10)
    
    def export_inventory_report(self):
        """تصدير تقرير المخزون"""
        try:
            from utils.helpers import export_to_excel
            
            filename = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                title="حفظ تقرير المخزون"
            )
            
            if not filename:
                return
            
            # جمع البيانات
            data = []
            columns = ["الكود", "اسم المنتج", "التصنيف", "الكمية الحالية", "الحد الأدنى", 
                      "قيمة المخزون", "حالة المخزون"]
            data.append(columns)
            
            products = Product.get_all()
            for product in products:
                if product.current_quantity == 0:
                    status = "نفد المخزون"
                elif product.current_quantity <= product.min_quantity:
                    status = "مخزون منخفض"
                else:
                    status = "مخزون عادي"
                
                value = product.current_quantity * product.cost_price
                
                data.append([
                    product.code,
                    product.name,
                    getattr(product, 'category_name', '') or '',
                    product.current_quantity,
                    product.min_quantity,
                    f"{value:.2f}",
                    status
                ])
            
            if export_to_excel(data, filename, "تقرير المخزون"):
                messagebox.showinfo("نجح", f"تم تصدير التقرير بنجاح إلى:\n{filename}")
            else:
                messagebox.showerror("خطأ", "فشل في تصدير التقرير")
                
        except ImportError:
            messagebox.showwarning("تحذير", "مكتبة openpyxl غير مثبتة")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء التصدير:\n{str(e)}")
    
    def export_transactions_report(self):
        """تصدير تقرير الحركات"""
        try:
            from utils.helpers import export_to_excel
            
            filename = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                title="حفظ تقرير الحركات"
            )
            
            if not filename:
                return
            
            # الحصول على المعاملات حسب الفترة المحددة
            period = self.report_period_var.get()
            end_date = datetime.now()
            
            if period == "آخر 7 أيام":
                start_date = end_date - timedelta(days=7)
            elif period == "آخر 30 يوم":
                start_date = end_date - timedelta(days=30)
            elif period == "آخر 3 أشهر":
                start_date = end_date - timedelta(days=90)
            else:
                start_date = end_date - timedelta(days=365)
            
            transactions = InventoryTransaction.get_by_date_range(
                start_date.strftime("%Y-%m-%d"),
                end_date.strftime("%Y-%m-%d")
            )
            
            # جمع البيانات
            data = []
            columns = ["التاريخ", "المنتج", "الكود", "النوع", "الكمية", "سعر الوحدة", 
                      "المبلغ الإجمالي", "المورد", "المستخدم"]
            data.append(columns)
            
            for transaction in transactions:
                transaction_type_ar = {
                    'in': 'وارد',
                    'out': 'صادر',
                    'adjustment': 'تعديل'
                }.get(transaction.transaction_type, transaction.transaction_type)
                
                data.append([
                    transaction.transaction_date.strftime("%Y-%m-%d %H:%M") if transaction.transaction_date else "",
                    transaction.product_name or "",
                    transaction.product_code or "",
                    transaction_type_ar,
                    transaction.quantity,
                    f"{transaction.unit_price:.2f}",
                    f"{transaction.total_amount:.2f}",
                    transaction.supplier_name or "",
                    transaction.user_name or ""
                ])
            
            if export_to_excel(data, filename, f"تقرير الحركات - {period}"):
                messagebox.showinfo("نجح", f"تم تصدير التقرير بنجاح إلى:\n{filename}")
            else:
                messagebox.showerror("خطأ", "فشل في تصدير التقرير")
                
        except ImportError:
            messagebox.showwarning("تحذير", "مكتبة openpyxl غير مثبتة")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء التصدير:\n{str(e)}")
