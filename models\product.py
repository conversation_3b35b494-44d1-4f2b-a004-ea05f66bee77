from config.database import db_manager
from datetime import datetime

class Product:
    def __init__(self, id=None, code=None, barcode=None, name=None, description=None,
                 category_id=None, unit=None, cost_price=0, selling_price=0,
                 min_quantity=0, max_quantity=1000, current_quantity=0,
                 image_path=None, is_active=True):
        self.id = id
        self.code = code
        self.barcode = barcode
        self.name = name
        self.description = description
        self.category_id = category_id
        self.unit = unit
        self.cost_price = cost_price
        self.selling_price = selling_price
        self.min_quantity = min_quantity
        self.max_quantity = max_quantity
        self.current_quantity = current_quantity
        self.image_path = image_path
        self.is_active = is_active
    
    def save(self):
        """حفظ المنتج في قاعدة البيانات"""
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        if self.id:
            # تحديث منتج موجود
            cursor.execute('''
                UPDATE products SET
                    code = ?, barcode = ?, name = ?, description = ?,
                    category_id = ?, unit = ?, cost_price = ?, selling_price = ?,
                    min_quantity = ?, max_quantity = ?, current_quantity = ?,
                    image_path = ?, is_active = ?
                WHERE id = ?
            ''', (self.code, self.barcode, self.name, self.description,
                  self.category_id, self.unit, self.cost_price, self.selling_price,
                  self.min_quantity, self.max_quantity, self.current_quantity,
                  self.image_path, self.is_active, self.id))
        else:
            # إضافة منتج جديد
            cursor.execute('''
                INSERT INTO products (
                    code, barcode, name, description, category_id, unit,
                    cost_price, selling_price, min_quantity, max_quantity,
                    current_quantity, image_path, is_active
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (self.code, self.barcode, self.name, self.description,
                  self.category_id, self.unit, self.cost_price, self.selling_price,
                  self.min_quantity, self.max_quantity, self.current_quantity,
                  self.image_path, self.is_active))
            
            self.id = cursor.lastrowid
        
        conn.commit()
        conn.close()
        return self.id
    
    def delete(self):
        """حذف المنتج (إلغاء تفعيل)"""
        if self.id:
            conn = db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('UPDATE products SET is_active = 0 WHERE id = ?', (self.id,))
            
            conn.commit()
            conn.close()
            self.is_active = False
    
    @staticmethod
    def get_by_id(product_id):
        """الحصول على منتج بالمعرف"""
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM products WHERE id = ?', (product_id,))
        row = cursor.fetchone()
        
        conn.close()
        
        if row:
            return Product(
                id=row['id'], code=row['code'], barcode=row['barcode'],
                name=row['name'], description=row['description'],
                category_id=row['category_id'], unit=row['unit'],
                cost_price=row['cost_price'], selling_price=row['selling_price'],
                min_quantity=row['min_quantity'], max_quantity=row['max_quantity'],
                current_quantity=row['current_quantity'], image_path=row['image_path'],
                is_active=row['is_active']
            )
        return None
    
    @staticmethod
    def get_by_code(code):
        """الحصول على منتج بالكود"""
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM products WHERE code = ? AND is_active = 1', (code,))
        row = cursor.fetchone()
        
        conn.close()
        
        if row:
            return Product(
                id=row['id'], code=row['code'], barcode=row['barcode'],
                name=row['name'], description=row['description'],
                category_id=row['category_id'], unit=row['unit'],
                cost_price=row['cost_price'], selling_price=row['selling_price'],
                min_quantity=row['min_quantity'], max_quantity=row['max_quantity'],
                current_quantity=row['current_quantity'], image_path=row['image_path'],
                is_active=row['is_active']
            )
        return None
    
    @staticmethod
    def get_all(active_only=True):
        """الحصول على جميع المنتجات"""
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        if active_only:
            cursor.execute('''
                SELECT p.*, c.name as category_name 
                FROM products p 
                LEFT JOIN categories c ON p.category_id = c.id 
                WHERE p.is_active = 1
                ORDER BY p.name
            ''')
        else:
            cursor.execute('''
                SELECT p.*, c.name as category_name 
                FROM products p 
                LEFT JOIN categories c ON p.category_id = c.id 
                ORDER BY p.name
            ''')
        
        rows = cursor.fetchall()
        conn.close()
        
        products = []
        for row in rows:
            product = Product(
                id=row['id'], code=row['code'], barcode=row['barcode'],
                name=row['name'], description=row['description'],
                category_id=row['category_id'], unit=row['unit'],
                cost_price=row['cost_price'], selling_price=row['selling_price'],
                min_quantity=row['min_quantity'], max_quantity=row['max_quantity'],
                current_quantity=row['current_quantity'], image_path=row['image_path'],
                is_active=row['is_active']
            )
            product.category_name = row['category_name']
            products.append(product)
        
        return products
    
    @staticmethod
    def search(query):
        """البحث في المنتجات"""
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT p.*, c.name as category_name 
            FROM products p 
            LEFT JOIN categories c ON p.category_id = c.id 
            WHERE p.is_active = 1 AND (
                p.name LIKE ? OR p.code LIKE ? OR p.barcode LIKE ? OR p.description LIKE ?
            )
            ORDER BY p.name
        ''', (f'%{query}%', f'%{query}%', f'%{query}%', f'%{query}%'))
        
        rows = cursor.fetchall()
        conn.close()
        
        products = []
        for row in rows:
            product = Product(
                id=row['id'], code=row['code'], barcode=row['barcode'],
                name=row['name'], description=row['description'],
                category_id=row['category_id'], unit=row['unit'],
                cost_price=row['cost_price'], selling_price=row['selling_price'],
                min_quantity=row['min_quantity'], max_quantity=row['max_quantity'],
                current_quantity=row['current_quantity'], image_path=row['image_path'],
                is_active=row['is_active']
            )
            product.category_name = row['category_name']
            products.append(product)
        
        return products
    
    @staticmethod
    def get_low_stock_products():
        """الحصول على المنتجات ذات المخزون المنخفض"""
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT p.*, c.name as category_name 
            FROM products p 
            LEFT JOIN categories c ON p.category_id = c.id 
            WHERE p.is_active = 1 AND p.current_quantity <= p.min_quantity
            ORDER BY p.current_quantity ASC
        ''')
        
        rows = cursor.fetchall()
        conn.close()
        
        products = []
        for row in rows:
            product = Product(
                id=row['id'], code=row['code'], barcode=row['barcode'],
                name=row['name'], description=row['description'],
                category_id=row['category_id'], unit=row['unit'],
                cost_price=row['cost_price'], selling_price=row['selling_price'],
                min_quantity=row['min_quantity'], max_quantity=row['max_quantity'],
                current_quantity=row['current_quantity'], image_path=row['image_path'],
                is_active=row['is_active']
            )
            product.category_name = row['category_name']
            products.append(product)
        
        return products
    
    def update_quantity(self, new_quantity):
        """تحديث كمية المنتج"""
        self.current_quantity = new_quantity
        
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE products SET current_quantity = ? WHERE id = ?
        ''', (new_quantity, self.id))
        
        conn.commit()
        conn.close()

class Category:
    def __init__(self, id=None, name=None, description=None):
        self.id = id
        self.name = name
        self.description = description
    
    def save(self):
        """حفظ التصنيف"""
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        if self.id:
            cursor.execute('''
                UPDATE categories SET name = ?, description = ? WHERE id = ?
            ''', (self.name, self.description, self.id))
        else:
            cursor.execute('''
                INSERT INTO categories (name, description) VALUES (?, ?)
            ''', (self.name, self.description))
            self.id = cursor.lastrowid
        
        conn.commit()
        conn.close()
        return self.id
    
    @staticmethod
    def get_all():
        """الحصول على جميع التصنيفات"""
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM categories ORDER BY name')
        rows = cursor.fetchall()
        
        conn.close()
        
        categories = []
        for row in rows:
            categories.append(Category(
                id=row['id'],
                name=row['name'],
                description=row['description']
            ))
        
        return categories
