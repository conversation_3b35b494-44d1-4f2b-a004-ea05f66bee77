import customtkinter as ctk
import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, date
import uuid

from models.product import Product
from models.transaction import InventoryTransaction, Customer, SalesOrder
from models.user import User
from utils.validators import Validator

class OutgoingView:
    def __init__(self, parent, main_window):
        self.parent = parent
        self.main_window = main_window
        self.current_user = main_window.current_user
        self.setup_ui()
        self.load_outgoing_data()

    def setup_ui(self):
        """إعداد واجهة الصادرات"""
        # إطار رئيسي
        self.main_frame = ctk.CTkFrame(self.parent)
        self.main_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # عنوان
        title = ctk.CTkLabel(
            self.main_frame,
            text="إدارة الصادرات والمبيعات",
            font=("Arial", 20, "bold")
        )
        title.pack(pady=10)

        # إطار التبويبات
        self.notebook = ttk.Notebook(self.main_frame)
        self.notebook.pack(fill="both", expand=True, padx=10, pady=10)

        # تبويب الصرف السريع
        self.create_quick_outgoing_tab()

        # تبويب أوامر البيع
        self.create_sales_orders_tab()

        # تبويب العملاء
        self.create_customers_tab()

        # تبويب تقارير الصادرات
        self.create_outgoing_reports_tab()

    def create_quick_outgoing_tab(self):
        """إنشاء تبويب الصرف السريع"""
        quick_frame = ctk.CTkFrame(self.notebook)
        self.notebook.add(quick_frame, text="صرف سريع")

        # إطار المحتوى
        content_frame = ctk.CTkScrollableFrame(quick_frame)
        content_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # عنوان
        title = ctk.CTkLabel(
            content_frame,
            text="صرف سريع للمنتجات",
            font=("Arial", 16, "bold")
        )
        title.pack(pady=10)

        # إطار البحث عن المنتج
        search_frame = ctk.CTkFrame(content_frame)
        search_frame.pack(fill="x", pady=10)

        ctk.CTkLabel(search_frame, text="البحث عن المنتج:", font=("Arial", 12, "bold")).pack(anchor="w", padx=10, pady=5)

        search_product_frame = ctk.CTkFrame(search_frame)
        search_product_frame.pack(fill="x", padx=10, pady=5)

        self.product_search_entry = ctk.CTkEntry(
            search_product_frame,
            placeholder_text="ابحث بالاسم أو الكود أو الباركود...",
            width=300
        )
        self.product_search_entry.pack(side="right", padx=5)
        self.product_search_entry.bind("<KeyRelease>", self.search_products_for_outgoing)

        search_btn = ctk.CTkButton(
            search_product_frame,
            text="🔍 بحث",
            command=self.search_products_for_outgoing,
            width=80
        )
        search_btn.pack(side="left", padx=5)

        # قائمة المنتجات المتاحة
        products_frame = ctk.CTkFrame(content_frame)
        products_frame.pack(fill="both", expand=True, pady=10)

        ctk.CTkLabel(products_frame, text="المنتجات المتاحة:", font=("Arial", 12, "bold")).pack(anchor="w", padx=10, pady=5)

        # جدول المنتجات
        columns = ("الكود", "اسم المنتج", "الكمية المتاحة", "سعر البيع", "الوحدة")
        self.products_tree = ttk.Treeview(products_frame, columns=columns, show="headings", height=8)

        for col in columns:
            self.products_tree.heading(col, text=col)
            self.products_tree.column(col, width=120, anchor="center")

        # شريط التمرير للمنتجات
        products_scrollbar = ttk.Scrollbar(products_frame, orient="vertical", command=self.products_tree.yview)
        self.products_tree.configure(yscrollcommand=products_scrollbar.set)

        self.products_tree.pack(side="right", fill="both", expand=True, padx=(10, 0), pady=10)
        products_scrollbar.pack(side="left", fill="y", pady=10)

        # ربط النقر المزدوج بإضافة المنتج
        self.products_tree.bind("<Double-1>", lambda e: self.add_product_to_outgoing())

        # أزرار التحكم
        buttons_frame = ctk.CTkFrame(content_frame)
        buttons_frame.pack(fill="x", pady=10)

        add_product_btn = ctk.CTkButton(
            buttons_frame,
            text="➕ إضافة للصرف",
            command=self.add_product_to_outgoing,
            width=150
        )
        add_product_btn.pack(side="right", padx=5)

        # إطار الصرف
        outgoing_frame = ctk.CTkFrame(content_frame)
        outgoing_frame.pack(fill="both", expand=True, pady=10)

        ctk.CTkLabel(outgoing_frame, text="قائمة الصرف:", font=("Arial", 12, "bold")).pack(anchor="w", padx=10, pady=5)

        # جدول الصرف
        outgoing_columns = ("المنتج", "الكمية", "سعر الوحدة", "الإجمالي", "ملاحظات")
        self.outgoing_tree = ttk.Treeview(outgoing_frame, columns=outgoing_columns, show="headings", height=6)

        for col in outgoing_columns:
            self.outgoing_tree.heading(col, text=col)
            self.outgoing_tree.column(col, width=120, anchor="center")

        # شريط التمرير للصرف
        outgoing_scrollbar = ttk.Scrollbar(outgoing_frame, orient="vertical", command=self.outgoing_tree.yview)
        self.outgoing_tree.configure(yscrollcommand=outgoing_scrollbar.set)

        self.outgoing_tree.pack(side="right", fill="both", expand=True, padx=(10, 0), pady=10)
        outgoing_scrollbar.pack(side="left", fill="y", pady=10)

        # ربط النقر المزدوج بتعديل الكمية
        self.outgoing_tree.bind("<Double-1>", lambda e: self.edit_outgoing_item())

        # إطار الإجمالي والحفظ
        total_frame = ctk.CTkFrame(content_frame)
        total_frame.pack(fill="x", pady=10)

        # الإجمالي
        self.total_label = ctk.CTkLabel(
            total_frame,
            text="الإجمالي: 0.00 ريال",
            font=("Arial", 14, "bold")
        )
        self.total_label.pack(side="left", padx=10, pady=10)

        # أزرار الحفظ
        save_buttons_frame = ctk.CTkFrame(total_frame)
        save_buttons_frame.pack(side="right", padx=10, pady=10)

        clear_btn = ctk.CTkButton(
            save_buttons_frame,
            text="🗑️ مسح الكل",
            command=self.clear_outgoing_list,
            width=100,
            fg_color="red"
        )
        clear_btn.pack(side="right", padx=5)

        save_btn = ctk.CTkButton(
            save_buttons_frame,
            text="💾 تنفيذ الصرف",
            command=self.execute_outgoing,
            width=120
        )
        save_btn.pack(side="right", padx=5)

        # قائمة المنتجات للصرف (مخفية)
        self.outgoing_items = []

        # تحميل المنتجات
        self.load_products_for_outgoing()

    def create_sales_orders_tab(self):
        """إنشاء تبويب أوامر البيع"""
        orders_frame = ctk.CTkFrame(self.notebook)
        self.notebook.add(orders_frame, text="أوامر البيع")

        # إطار المحتوى
        content_frame = ctk.CTkFrame(orders_frame)
        content_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # عنوان وأزرار
        header_frame = ctk.CTkFrame(content_frame)
        header_frame.pack(fill="x", pady=10)

        title = ctk.CTkLabel(
            header_frame,
            text="أوامر البيع",
            font=("Arial", 16, "bold")
        )
        title.pack(side="right", padx=10)

        # أزرار التحكم
        new_order_btn = ctk.CTkButton(
            header_frame,
            text="➕ أمر بيع جديد",
            command=self.create_new_sales_order,
            width=150
        )
        new_order_btn.pack(side="left", padx=5)

        # جدول أوامر البيع
        orders_table_frame = ctk.CTkFrame(content_frame)
        orders_table_frame.pack(fill="both", expand=True, pady=10)

        columns = ("رقم الأمر", "العميل", "التاريخ", "تاريخ التسليم", "الحالة", "المبلغ الإجمالي", "المستخدم")
        self.orders_tree = ttk.Treeview(orders_table_frame, columns=columns, show="headings", height=15)

        for col in columns:
            self.orders_tree.heading(col, text=col)
            self.orders_tree.column(col, width=120, anchor="center")

        # شريط التمرير
        orders_scrollbar = ttk.Scrollbar(orders_table_frame, orient="vertical", command=self.orders_tree.yview)
        self.orders_tree.configure(yscrollcommand=orders_scrollbar.set)

        self.orders_tree.pack(side="right", fill="both", expand=True, padx=10, pady=10)
        orders_scrollbar.pack(side="left", fill="y", pady=10)

        # تحميل أوامر البيع
        self.load_sales_orders()

    def create_customers_tab(self):
        """إنشاء تبويب العملاء"""
        customers_frame = ctk.CTkFrame(self.notebook)
        self.notebook.add(customers_frame, text="العملاء")

        # إطار المحتوى
        content_frame = ctk.CTkFrame(customers_frame)
        content_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # عنوان وأزرار
        header_frame = ctk.CTkFrame(content_frame)
        header_frame.pack(fill="x", pady=10)

        title = ctk.CTkLabel(
            header_frame,
            text="إدارة العملاء",
            font=("Arial", 16, "bold")
        )
        title.pack(side="right", padx=10)

        # أزرار التحكم
        new_customer_btn = ctk.CTkButton(
            header_frame,
            text="➕ عميل جديد",
            command=self.add_new_customer,
            width=120
        )
        new_customer_btn.pack(side="left", padx=5)

        edit_customer_btn = ctk.CTkButton(
            header_frame,
            text="✏️ تعديل",
            command=self.edit_customer,
            width=100
        )
        edit_customer_btn.pack(side="left", padx=5)

        # جدول العملاء
        customers_table_frame = ctk.CTkFrame(content_frame)
        customers_table_frame.pack(fill="both", expand=True, pady=10)

        columns = ("الاسم", "الشخص المسؤول", "الهاتف", "البريد الإلكتروني", "النوع", "الحالة")
        self.customers_tree = ttk.Treeview(customers_table_frame, columns=columns, show="headings", height=15)

        for col in columns:
            self.customers_tree.heading(col, text=col)
            self.customers_tree.column(col, width=150, anchor="center")

        # شريط التمرير
        customers_scrollbar = ttk.Scrollbar(customers_table_frame, orient="vertical", command=self.customers_tree.yview)
        self.customers_tree.configure(yscrollcommand=customers_scrollbar.set)

        self.customers_tree.pack(side="right", fill="both", expand=True, padx=10, pady=10)
        customers_scrollbar.pack(side="left", fill="y", pady=10)

        # تحميل العملاء
        self.load_customers()

    def create_outgoing_reports_tab(self):
        """إنشاء تبويب تقارير الصادرات"""
        reports_frame = ctk.CTkFrame(self.notebook)
        self.notebook.add(reports_frame, text="تقارير الصادرات")

        # إطار المحتوى
        content_frame = ctk.CTkFrame(reports_frame)
        content_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # عنوان
        title = ctk.CTkLabel(
            content_frame,
            text="تقارير الصادرات والمبيعات",
            font=("Arial", 16, "bold")
        )
        title.pack(pady=20)

        # أزرار التقارير
        reports_buttons_frame = ctk.CTkFrame(content_frame)
        reports_buttons_frame.pack(fill="x", pady=20)

        daily_report_btn = ctk.CTkButton(
            reports_buttons_frame,
            text="📊 تقرير يومي",
            command=self.show_daily_outgoing_report,
            width=150,
            height=40
        )
        daily_report_btn.pack(side="right", padx=10)

        monthly_report_btn = ctk.CTkButton(
            reports_buttons_frame,
            text="📈 تقرير شهري",
            command=self.show_monthly_outgoing_report,
            width=150,
            height=40
        )
        monthly_report_btn.pack(side="right", padx=10)

        customer_report_btn = ctk.CTkButton(
            reports_buttons_frame,
            text="👥 تقرير العملاء",
            command=self.show_customers_report,
            width=150,
            height=40
        )
        customer_report_btn.pack(side="left", padx=10)

        # إطار عرض التقارير
        self.reports_display_frame = ctk.CTkFrame(content_frame)
        self.reports_display_frame.pack(fill="both", expand=True, pady=20)

    def load_outgoing_data(self):
        """تحميل بيانات الصادرات"""
        self.load_products_for_outgoing()
        self.load_sales_orders()
        self.load_customers()

    def load_products_for_outgoing(self):
        """تحميل المنتجات المتاحة للصرف"""
        # مسح البيانات الحالية
        for item in self.products_tree.get_children():
            self.products_tree.delete(item)

        # تحميل المنتجات المتاحة (التي لديها مخزون)
        products = Product.get_all()

        for product in products:
            if product.current_quantity > 0 and product.is_active:
                self.products_tree.insert("", "end", values=(
                    product.code,
                    product.name,
                    product.current_quantity,
                    f"{product.selling_price:.2f}",
                    product.unit
                ))

    def search_products_for_outgoing(self, event=None):
        """البحث في المنتجات للصرف"""
        query = self.product_search_entry.get().strip().lower()

        # مسح البيانات الحالية
        for item in self.products_tree.get_children():
            self.products_tree.delete(item)

        if not query:
            self.load_products_for_outgoing()
            return

        # البحث في المنتجات
        products = Product.get_all()

        for product in products:
            if (product.current_quantity > 0 and product.is_active and
                (query in product.name.lower() or
                 query in product.code.lower() or
                 (product.barcode and query in product.barcode.lower()))):

                self.products_tree.insert("", "end", values=(
                    product.code,
                    product.name,
                    product.current_quantity,
                    f"{product.selling_price:.2f}",
                    product.unit
                ))

    def add_product_to_outgoing(self):
        """إضافة منتج لقائمة الصرف"""
        selection = self.products_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار منتج لإضافته للصرف")
            return

        item = self.products_tree.item(selection[0])
        product_code = item['values'][0]
        product_name = item['values'][1]
        available_quantity = int(item['values'][2])
        unit_price = float(item['values'][3])

        # نافذة إدخال الكمية
        quantity_window = ctk.CTkToplevel(self.parent)
        quantity_window.title(f"صرف {product_name}")
        quantity_window.geometry("400x300")
        quantity_window.transient(self.parent)
        quantity_window.grab_set()

        # إطار رئيسي
        main_frame = ctk.CTkFrame(quantity_window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # معلومات المنتج
        info_frame = ctk.CTkFrame(main_frame)
        info_frame.pack(fill="x", pady=10)

        ctk.CTkLabel(info_frame, text=f"المنتج: {product_name}", font=("Arial", 12, "bold")).pack(pady=5)
        ctk.CTkLabel(info_frame, text=f"الكمية المتاحة: {available_quantity}").pack(pady=2)
        ctk.CTkLabel(info_frame, text=f"سعر الوحدة: {unit_price:.2f} ريال").pack(pady=2)

        # إدخال الكمية
        quantity_frame = ctk.CTkFrame(main_frame)
        quantity_frame.pack(fill="x", pady=10)

        ctk.CTkLabel(quantity_frame, text="الكمية المطلوبة:", width=120).pack(side="right", padx=5)
        quantity_entry = ctk.CTkEntry(quantity_frame, width=200)
        quantity_entry.pack(side="left", padx=5)
        quantity_entry.insert(0, "1")

        # سعر الوحدة (قابل للتعديل)
        price_frame = ctk.CTkFrame(main_frame)
        price_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(price_frame, text="سعر الوحدة:", width=120).pack(side="right", padx=5)
        price_entry = ctk.CTkEntry(price_frame, width=200)
        price_entry.pack(side="left", padx=5)
        price_entry.insert(0, str(unit_price))

        # ملاحظات
        notes_frame = ctk.CTkFrame(main_frame)
        notes_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(notes_frame, text="ملاحظات:", width=120).pack(side="right", padx=5)
        notes_entry = ctk.CTkEntry(notes_frame, width=200)
        notes_entry.pack(side="left", padx=5)

        # أزرار
        buttons_frame = ctk.CTkFrame(main_frame)
        buttons_frame.pack(fill="x", pady=20)

        def add_to_list():
            try:
                quantity = int(quantity_entry.get())
                price = float(price_entry.get())
                notes = notes_entry.get().strip()

                if quantity <= 0:
                    messagebox.showerror("خطأ", "الكمية يجب أن تكون أكبر من صفر")
                    return

                if quantity > available_quantity:
                    messagebox.showerror("خطأ", f"الكمية المطلوبة أكبر من المتاح ({available_quantity})")
                    return

                if price < 0:
                    messagebox.showerror("خطأ", "السعر يجب أن يكون موجب")
                    return

                # إضافة للقائمة
                total = quantity * price

                # التحقق من وجود المنتج في القائمة
                existing_item = None
                for item_id in self.outgoing_tree.get_children():
                    item_values = self.outgoing_tree.item(item_id)['values']
                    if item_values[0] == product_name:
                        existing_item = item_id
                        break

                if existing_item:
                    # تحديث الكمية الموجودة
                    old_values = self.outgoing_tree.item(existing_item)['values']
                    old_quantity = int(old_values[1])
                    new_quantity = old_quantity + quantity
                    new_total = new_quantity * price

                    self.outgoing_tree.item(existing_item, values=(
                        product_name, new_quantity, f"{price:.2f}", f"{new_total:.2f}", notes
                    ))
                else:
                    # إضافة عنصر جديد
                    self.outgoing_tree.insert("", "end", values=(
                        product_name, quantity, f"{price:.2f}", f"{total:.2f}", notes
                    ))

                # إضافة للقائمة المخفية
                item_data = {
                    'product_code': product_code,
                    'product_name': product_name,
                    'quantity': quantity,
                    'unit_price': price,
                    'total': total,
                    'notes': notes
                }

                # البحث عن العنصر الموجود
                existing_index = None
                for i, item in enumerate(self.outgoing_items):
                    if item['product_code'] == product_code:
                        existing_index = i
                        break

                if existing_index is not None:
                    # تحديث الكمية
                    self.outgoing_items[existing_index]['quantity'] += quantity
                    self.outgoing_items[existing_index]['total'] = (
                        self.outgoing_items[existing_index]['quantity'] *
                        self.outgoing_items[existing_index]['unit_price']
                    )
                else:
                    self.outgoing_items.append(item_data)

                # تحديث الإجمالي
                self.update_outgoing_total()

                quantity_window.destroy()

            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال قيم صحيحة")

        add_btn = ctk.CTkButton(
            buttons_frame,
            text="➕ إضافة",
            command=add_to_list,
            width=120
        )
        add_btn.pack(side="left", padx=10)

        cancel_btn = ctk.CTkButton(
            buttons_frame,
            text="❌ إلغاء",
            command=quantity_window.destroy,
            width=120,
            fg_color="gray"
        )
        cancel_btn.pack(side="right", padx=10)

    def edit_outgoing_item(self):
        """تعديل عنصر في قائمة الصرف"""
        selection = self.outgoing_tree.selection()
        if not selection:
            return

        item = self.outgoing_tree.item(selection[0])
        product_name = item['values'][0]
        current_quantity = int(item['values'][1])
        current_price = float(item['values'][2])
        current_notes = item['values'][4]

        # نافذة التعديل
        edit_window = ctk.CTkToplevel(self.parent)
        edit_window.title(f"تعديل {product_name}")
        edit_window.geometry("400x250")
        edit_window.transient(self.parent)
        edit_window.grab_set()

        # إطار رئيسي
        main_frame = ctk.CTkFrame(edit_window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # الكمية
        quantity_frame = ctk.CTkFrame(main_frame)
        quantity_frame.pack(fill="x", pady=10)

        ctk.CTkLabel(quantity_frame, text="الكمية:", width=120).pack(side="right", padx=5)
        quantity_entry = ctk.CTkEntry(quantity_frame, width=200)
        quantity_entry.pack(side="left", padx=5)
        quantity_entry.insert(0, str(current_quantity))

        # السعر
        price_frame = ctk.CTkFrame(main_frame)
        price_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(price_frame, text="سعر الوحدة:", width=120).pack(side="right", padx=5)
        price_entry = ctk.CTkEntry(price_frame, width=200)
        price_entry.pack(side="left", padx=5)
        price_entry.insert(0, str(current_price))

        # ملاحظات
        notes_frame = ctk.CTkFrame(main_frame)
        notes_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(notes_frame, text="ملاحظات:", width=120).pack(side="right", padx=5)
        notes_entry = ctk.CTkEntry(notes_frame, width=200)
        notes_entry.pack(side="left", padx=5)
        notes_entry.insert(0, current_notes)

        # أزرار
        buttons_frame = ctk.CTkFrame(main_frame)
        buttons_frame.pack(fill="x", pady=20)

        def update_item():
            try:
                new_quantity = int(quantity_entry.get())
                new_price = float(price_entry.get())
                new_notes = notes_entry.get().strip()

                if new_quantity <= 0:
                    messagebox.showerror("خطأ", "الكمية يجب أن تكون أكبر من صفر")
                    return

                if new_price < 0:
                    messagebox.showerror("خطأ", "السعر يجب أن يكون موجب")
                    return

                # تحديث الجدول
                new_total = new_quantity * new_price
                self.outgoing_tree.item(selection[0], values=(
                    product_name, new_quantity, f"{new_price:.2f}", f"{new_total:.2f}", new_notes
                ))

                # تحديث القائمة المخفية
                for item in self.outgoing_items:
                    if item['product_name'] == product_name:
                        item['quantity'] = new_quantity
                        item['unit_price'] = new_price
                        item['total'] = new_total
                        item['notes'] = new_notes
                        break

                # تحديث الإجمالي
                self.update_outgoing_total()

                edit_window.destroy()

            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال قيم صحيحة")

        def remove_item():
            # حذف من الجدول
            self.outgoing_tree.delete(selection[0])

            # حذف من القائمة المخفية
            self.outgoing_items = [item for item in self.outgoing_items if item['product_name'] != product_name]

            # تحديث الإجمالي
            self.update_outgoing_total()

            edit_window.destroy()

        update_btn = ctk.CTkButton(
            buttons_frame,
            text="💾 تحديث",
            command=update_item,
            width=100
        )
        update_btn.pack(side="left", padx=5)

        remove_btn = ctk.CTkButton(
            buttons_frame,
            text="🗑️ حذف",
            command=remove_item,
            width=100,
            fg_color="red"
        )
        remove_btn.pack(side="left", padx=5)

        cancel_btn = ctk.CTkButton(
            buttons_frame,
            text="❌ إلغاء",
            command=edit_window.destroy,
            width=100,
            fg_color="gray"
        )
        cancel_btn.pack(side="right", padx=5)

    def update_outgoing_total(self):
        """تحديث إجمالي الصرف"""
        total = sum(item['total'] for item in self.outgoing_items)
        self.total_label.configure(text=f"الإجمالي: {total:.2f} ريال")

    def clear_outgoing_list(self):
        """مسح قائمة الصرف"""
        result = messagebox.askyesno("تأكيد", "هل أنت متأكد من مسح جميع العناصر؟")
        if result:
            # مسح الجدول
            for item in self.outgoing_tree.get_children():
                self.outgoing_tree.delete(item)

            # مسح القائمة
            self.outgoing_items.clear()

            # تحديث الإجمالي
            self.update_outgoing_total()

    def execute_outgoing(self):
        """تنفيذ عملية الصرف"""
        if not self.outgoing_items:
            messagebox.showwarning("تحذير", "لا توجد عناصر للصرف")
            return

        # نافذة تأكيد الصرف
        confirm_window = ctk.CTkToplevel(self.parent)
        confirm_window.title("تأكيد الصرف")
        confirm_window.geometry("500x400")
        confirm_window.transient(self.parent)
        confirm_window.grab_set()

        # إطار رئيسي
        main_frame = ctk.CTkScrollableFrame(confirm_window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # عنوان
        title = ctk.CTkLabel(
            main_frame,
            text="تأكيد عملية الصرف",
            font=("Arial", 16, "bold")
        )
        title.pack(pady=10)

        # معلومات إضافية
        info_frame = ctk.CTkFrame(main_frame)
        info_frame.pack(fill="x", pady=10)

        # رقم المرجع
        ref_frame = ctk.CTkFrame(info_frame)
        ref_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(ref_frame, text="رقم المرجع:", width=120).pack(side="right", padx=5)
        ref_entry = ctk.CTkEntry(ref_frame, width=300)
        ref_entry.pack(side="left", padx=5)
        ref_entry.insert(0, f"OUT-{datetime.now().strftime('%Y%m%d%H%M%S')}")

        # ملاحظات عامة
        notes_frame = ctk.CTkFrame(info_frame)
        notes_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(notes_frame, text="ملاحظات عامة:", width=120).pack(side="right", padx=5)
        notes_entry = ctk.CTkTextbox(notes_frame, width=300, height=60)
        notes_entry.pack(side="left", padx=5)

        # ملخص الصرف
        summary_frame = ctk.CTkFrame(main_frame)
        summary_frame.pack(fill="x", pady=10)

        ctk.CTkLabel(summary_frame, text="ملخص الصرف:", font=("Arial", 12, "bold")).pack(anchor="w", padx=10, pady=5)

        total_amount = sum(item['total'] for item in self.outgoing_items)
        total_items = len(self.outgoing_items)

        ctk.CTkLabel(summary_frame, text=f"عدد الأصناف: {total_items}").pack(anchor="w", padx=20, pady=2)
        ctk.CTkLabel(summary_frame, text=f"إجمالي المبلغ: {total_amount:.2f} ريال").pack(anchor="w", padx=20, pady=2)

        # أزرار التأكيد
        buttons_frame = ctk.CTkFrame(main_frame)
        buttons_frame.pack(fill="x", pady=20)

        def confirm_outgoing():
            try:
                reference_number = ref_entry.get().strip()
                general_notes = notes_entry.get("1.0", "end-1c").strip()

                if not reference_number:
                    messagebox.showerror("خطأ", "يرجى إدخال رقم المرجع")
                    return

                # تنفيذ المعاملات
                success_count = 0
                failed_items = []

                for item in self.outgoing_items:
                    try:
                        # البحث عن المنتج
                        product = None
                        products = Product.get_all()
                        for p in products:
                            if p.code == item['product_code']:
                                product = p
                                break

                        if not product:
                            failed_items.append(f"{item['product_name']} - المنتج غير موجود")
                            continue

                        if product.current_quantity < item['quantity']:
                            failed_items.append(f"{item['product_name']} - كمية غير كافية")
                            continue

                        # إنشاء معاملة الصرف
                        transaction = InventoryTransaction(
                            product_id=product.id,
                            transaction_type='out',
                            quantity=item['quantity'],
                            unit_price=item['unit_price'],
                            total_amount=item['total'],
                            reference_number=reference_number,
                            user_id=self.current_user.id,
                            notes=f"{general_notes} | {item['notes']}" if item['notes'] else general_notes,
                            transaction_date=datetime.now()
                        )

                        transaction.save()
                        success_count += 1

                    except Exception as e:
                        failed_items.append(f"{item['product_name']} - خطأ: {str(e)}")

                # عرض النتائج
                if success_count > 0:
                    if failed_items:
                        message = f"تم صرف {success_count} عنصر بنجاح\n\nفشل في صرف:\n" + "\n".join(failed_items)
                        messagebox.showwarning("تم جزئياً", message)
                    else:
                        messagebox.showinfo("نجح", f"تم صرف جميع العناصر ({success_count}) بنجاح")
                else:
                    messagebox.showerror("فشل", "فشل في صرف جميع العناصر:\n" + "\n".join(failed_items))

                if success_count > 0:
                    # مسح قائمة الصرف
                    self.outgoing_items.clear()
                    for item in self.outgoing_tree.get_children():
                        self.outgoing_tree.delete(item)
                    self.update_outgoing_total()

                    # تحديث قائمة المنتجات
                    self.load_products_for_outgoing()

                confirm_window.destroy()

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء تنفيذ الصرف:\n{str(e)}")

        confirm_btn = ctk.CTkButton(
            buttons_frame,
            text="✅ تأكيد الصرف",
            command=confirm_outgoing,
            width=150
        )
        confirm_btn.pack(side="left", padx=10)

        cancel_btn = ctk.CTkButton(
            buttons_frame,
            text="❌ إلغاء",
            command=confirm_window.destroy,
            width=120,
            fg_color="gray"
        )
        cancel_btn.pack(side="right", padx=10)

    def load_sales_orders(self):
        """تحميل أوامر البيع"""
        # مسح البيانات الحالية
        for item in self.orders_tree.get_children():
            self.orders_tree.delete(item)

        try:
            # تحميل أوامر البيع
            orders = SalesOrder.get_all()

            for order in orders:
                # تحويل الحالة إلى العربية
                status_ar = {
                    'pending': 'معلق',
                    'confirmed': 'مؤكد',
                    'delivered': 'تم التسليم',
                    'cancelled': 'ملغي'
                }.get(order.status, order.status)

                # تنسيق التواريخ
                order_date = order.order_date.strftime("%Y-%m-%d") if order.order_date else ""
                delivery_date = order.delivery_date.strftime("%Y-%m-%d") if order.delivery_date else ""

                self.orders_tree.insert("", "end", values=(
                    order.order_number or "",
                    order.customer_name or "",
                    order_date,
                    delivery_date,
                    status_ar,
                    f"{order.final_amount:.2f}",
                    order.user_name or ""
                ))
        except Exception as e:
            print(f"خطأ في تحميل أوامر البيع: {e}")

    def load_customers(self):
        """تحميل العملاء"""
        # مسح البيانات الحالية
        for item in self.customers_tree.get_children():
            self.customers_tree.delete(item)

        try:
            # تحميل العملاء
            customers = Customer.get_all()

            for customer in customers:
                # تحويل النوع إلى العربية
                type_ar = {
                    'individual': 'فرد',
                    'company': 'شركة'
                }.get(customer.customer_type, customer.customer_type)

                status = "نشط" if customer.is_active else "غير نشط"

                self.customers_tree.insert("", "end", values=(
                    customer.name,
                    customer.contact_person or "",
                    customer.phone or "",
                    customer.email or "",
                    type_ar,
                    status
                ))
        except Exception as e:
            print(f"خطأ في تحميل العملاء: {e}")

    def create_new_sales_order(self):
        """إنشاء أمر بيع جديد"""
        messagebox.showinfo("قيد التطوير", "واجهة إنشاء أوامر البيع قيد التطوير...")

    def add_new_customer(self):
        """إضافة عميل جديد"""
        self.show_customer_form()

    def edit_customer(self):
        """تعديل عميل"""
        selection = self.customers_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار عميل للتعديل")
            return

        item = self.customers_tree.item(selection[0])
        customer_name = item['values'][0]

        # البحث عن العميل
        customers = Customer.get_all()
        selected_customer = None
        for customer in customers:
            if customer.name == customer_name:
                selected_customer = customer
                break

        if selected_customer:
            self.show_customer_form(selected_customer)

    def show_customer_form(self, customer=None):
        """عرض نموذج العميل"""
        # نافذة النموذج
        form_window = ctk.CTkToplevel(self.parent)
        form_window.title("إضافة عميل" if not customer else "تعديل عميل")
        form_window.geometry("500x600")
        form_window.transient(self.parent)
        form_window.grab_set()

        # إطار رئيسي
        main_frame = ctk.CTkScrollableFrame(form_window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # عنوان
        title = ctk.CTkLabel(
            main_frame,
            text="إضافة عميل جديد" if not customer else "تعديل العميل",
            font=("Arial", 16, "bold")
        )
        title.pack(pady=10)

        # حقول النموذج
        fields = {}

        # اسم العميل
        name_frame = ctk.CTkFrame(main_frame)
        name_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(name_frame, text="اسم العميل *", width=120).pack(side="right", padx=5)
        fields['name'] = ctk.CTkEntry(name_frame, width=300)
        fields['name'].pack(side="left", padx=5)

        # الشخص المسؤول
        contact_frame = ctk.CTkFrame(main_frame)
        contact_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(contact_frame, text="الشخص المسؤول", width=120).pack(side="right", padx=5)
        fields['contact_person'] = ctk.CTkEntry(contact_frame, width=300)
        fields['contact_person'].pack(side="left", padx=5)

        # رقم الهاتف
        phone_frame = ctk.CTkFrame(main_frame)
        phone_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(phone_frame, text="رقم الهاتف", width=120).pack(side="right", padx=5)
        fields['phone'] = ctk.CTkEntry(phone_frame, width=300)
        fields['phone'].pack(side="left", padx=5)

        # البريد الإلكتروني
        email_frame = ctk.CTkFrame(main_frame)
        email_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(email_frame, text="البريد الإلكتروني", width=120).pack(side="right", padx=5)
        fields['email'] = ctk.CTkEntry(email_frame, width=300)
        fields['email'].pack(side="left", padx=5)

        # العنوان
        address_frame = ctk.CTkFrame(main_frame)
        address_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(address_frame, text="العنوان", width=120).pack(side="right", padx=5)
        fields['address'] = ctk.CTkTextbox(address_frame, width=300, height=80)
        fields['address'].pack(side="left", padx=5)

        # الرقم الضريبي
        tax_frame = ctk.CTkFrame(main_frame)
        tax_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(tax_frame, text="الرقم الضريبي", width=120).pack(side="right", padx=5)
        fields['tax_number'] = ctk.CTkEntry(tax_frame, width=300)
        fields['tax_number'].pack(side="left", padx=5)

        # نوع العميل
        type_frame = ctk.CTkFrame(main_frame)
        type_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(type_frame, text="نوع العميل", width=120).pack(side="right", padx=5)
        fields['customer_type'] = ctk.CTkComboBox(
            type_frame,
            values=["فرد", "شركة"],
            width=300
        )
        fields['customer_type'].pack(side="left", padx=5)

        # الحالة
        status_frame = ctk.CTkFrame(main_frame)
        status_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(status_frame, text="الحالة", width=120).pack(side="right", padx=5)
        fields['is_active'] = ctk.CTkCheckBox(status_frame, text="نشط")
        fields['is_active'].pack(side="left", padx=5)

        # ملء البيانات في حالة التعديل
        if customer:
            fields['name'].insert(0, customer.name or "")
            fields['contact_person'].insert(0, customer.contact_person or "")
            fields['phone'].insert(0, customer.phone or "")
            fields['email'].insert(0, customer.email or "")
            fields['address'].insert("1.0", customer.address or "")
            fields['tax_number'].insert(0, customer.tax_number or "")

            type_ar = "شركة" if customer.customer_type == "company" else "فرد"
            fields['customer_type'].set(type_ar)

            if customer.is_active:
                fields['is_active'].select()
        else:
            # قيم افتراضية
            fields['customer_type'].set("فرد")
            fields['is_active'].select()

        # أزرار الحفظ والإلغاء
        buttons_frame = ctk.CTkFrame(main_frame)
        buttons_frame.pack(fill="x", pady=20)

        def save_customer():
            try:
                # جمع البيانات
                name = fields['name'].get().strip()
                contact_person = fields['contact_person'].get().strip()
                phone = fields['phone'].get().strip()
                email = fields['email'].get().strip()
                address = fields['address'].get("1.0", "end-1c").strip()
                tax_number = fields['tax_number'].get().strip()
                customer_type_ar = fields['customer_type'].get()
                is_active = fields['is_active'].get()

                # التحقق من البيانات
                if not name:
                    messagebox.showerror("خطأ", "اسم العميل مطلوب")
                    return

                # تحويل نوع العميل
                customer_type = "company" if customer_type_ar == "شركة" else "individual"

                if customer:
                    # تحديث عميل موجود
                    customer.name = name
                    customer.contact_person = contact_person
                    customer.phone = phone
                    customer.email = email
                    customer.address = address
                    customer.tax_number = tax_number
                    customer.customer_type = customer_type
                    customer.is_active = is_active

                    customer.save()
                    messagebox.showinfo("نجح", "تم تحديث العميل بنجاح")
                else:
                    # إضافة عميل جديد
                    new_customer = Customer(
                        name=name,
                        contact_person=contact_person,
                        phone=phone,
                        email=email,
                        address=address,
                        tax_number=tax_number,
                        customer_type=customer_type,
                        is_active=is_active
                    )

                    new_customer.save()
                    messagebox.showinfo("نجح", "تم إضافة العميل بنجاح")

                # إغلاق النموذج وتحديث القائمة
                form_window.destroy()
                self.load_customers()

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ العميل:\n{str(e)}")

        save_btn = ctk.CTkButton(
            buttons_frame,
            text="💾 حفظ",
            command=save_customer,
            width=120
        )
        save_btn.pack(side="left", padx=10)

        cancel_btn = ctk.CTkButton(
            buttons_frame,
            text="❌ إلغاء",
            command=form_window.destroy,
            width=120,
            fg_color="gray"
        )
        cancel_btn.pack(side="right", padx=10)

    def show_daily_outgoing_report(self):
        """عرض تقرير الصادرات اليومي"""
        messagebox.showinfo("قيد التطوير", "تقرير الصادرات اليومي قيد التطوير...")

    def show_monthly_outgoing_report(self):
        """عرض تقرير الصادرات الشهري"""
        messagebox.showinfo("قيد التطوير", "تقرير الصادرات الشهري قيد التطوير...")

    def show_customers_report(self):
        """عرض تقرير العملاء"""
        messagebox.showinfo("قيد التطوير", "تقرير العملاء قيد التطوير...")