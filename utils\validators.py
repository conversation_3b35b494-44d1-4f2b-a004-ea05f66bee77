import re
from datetime import datetime, date

class Validator:
    """فئة للتحقق من صحة البيانات"""
    
    @staticmethod
    def is_not_empty(value):
        """التحقق من أن القيمة ليست فارغة"""
        if value is None:
            return False
        if isinstance(value, str):
            return value.strip() != ""
        return True
    
    @staticmethod
    def is_valid_number(value, min_value=None, max_value=None, allow_negative=False):
        """التحقق من صحة الرقم"""
        try:
            num = float(value)
            
            if not allow_negative and num < 0:
                return False
            
            if min_value is not None and num < min_value:
                return False
            
            if max_value is not None and num > max_value:
                return False
            
            return True
        except (ValueError, TypeError):
            return False
    
    @staticmethod
    def is_valid_integer(value, min_value=None, max_value=None, allow_negative=False):
        """التحقق من صحة الرقم الصحيح"""
        try:
            num = int(value)
            
            if not allow_negative and num < 0:
                return False
            
            if min_value is not None and num < min_value:
                return False
            
            if max_value is not None and num > max_value:
                return False
            
            return True
        except (ValueError, TypeError):
            return False
    
    @staticmethod
    def is_valid_email(email):
        """التحقق من صحة البريد الإلكتروني"""
        if not email:
            return False
        
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email.strip()) is not None
    
    @staticmethod
    def is_valid_phone(phone):
        """التحقق من صحة رقم الهاتف السعودي"""
        if not phone:
            return False
        
        # إزالة المسافات والرموز
        clean_phone = re.sub(r'[^\d]', '', phone)
        
        # نمط للأرقام السعودية
        patterns = [
            r'^05[0-9]{8}$',  # 05xxxxxxxx
            r'^5[0-9]{8}$',   # 5xxxxxxxx
            r'^9665[0-9]{8}$', # 9665xxxxxxxx
            r'^\+9665[0-9]{8}$' # +9665xxxxxxxx
        ]
        
        return any(re.match(pattern, clean_phone) for pattern in patterns)
    
    @staticmethod
    def is_valid_date(date_str, format_str="%Y-%m-%d"):
        """التحقق من صحة التاريخ"""
        try:
            datetime.strptime(date_str, format_str)
            return True
        except (ValueError, TypeError):
            return False
    
    @staticmethod
    def is_valid_barcode(barcode):
        """التحقق من صحة الباركود"""
        if not barcode:
            return False
        
        # إزالة المسافات
        clean_barcode = barcode.strip()
        
        # التحقق من الطول (عادة بين 8-14 رقم)
        if len(clean_barcode) < 8 or len(clean_barcode) > 14:
            return False
        
        # التحقق من أن جميع الأحرف أرقام
        return clean_barcode.isdigit()
    
    @staticmethod
    def is_valid_product_code(code):
        """التحقق من صحة كود المنتج"""
        if not code:
            return False
        
        code = code.strip()
        
        # يجب أن يكون بين 3-20 حرف
        if len(code) < 3 or len(code) > 20:
            return False
        
        # يجب أن يحتوي على أحرف وأرقام فقط
        return re.match(r'^[A-Za-z0-9_-]+$', code) is not None
    
    @staticmethod
    def is_valid_username(username):
        """التحقق من صحة اسم المستخدم"""
        if not username:
            return False
        
        username = username.strip()
        
        # يجب أن يكون بين 3-20 حرف
        if len(username) < 3 or len(username) > 20:
            return False
        
        # يجب أن يبدأ بحرف
        if not username[0].isalpha():
            return False
        
        # يجب أن يحتوي على أحرف وأرقام فقط
        return re.match(r'^[A-Za-z0-9_]+$', username) is not None
    
    @staticmethod
    def is_valid_password(password):
        """التحقق من قوة كلمة المرور"""
        if not password:
            return False
        
        # يجب أن تكون على الأقل 6 أحرف
        if len(password) < 6:
            return False
        
        return True
    
    @staticmethod
    def is_valid_tax_number(tax_number):
        """التحقق من صحة الرقم الضريبي السعودي"""
        if not tax_number:
            return True  # اختياري
        
        # إزالة المسافات والرموز
        clean_tax = re.sub(r'[^\d]', '', tax_number)
        
        # الرقم الضريبي السعودي 15 رقم
        return len(clean_tax) == 15 and clean_tax.isdigit()
    
    @staticmethod
    def is_valid_url(url):
        """التحقق من صحة الرابط"""
        if not url:
            return True  # اختياري
        
        pattern = r'^https?://(?:[-\w.])+(?:[:\d]+)?(?:/(?:[\w/_.])*(?:\?(?:[\w&=%.])*)?(?:#(?:\w*))?)?$'
        return re.match(pattern, url) is not None

class ProductValidator:
    """فئة للتحقق من صحة بيانات المنتج"""
    
    @staticmethod
    def validate_product_data(data):
        """التحقق من صحة بيانات المنتج"""
        errors = []
        
        # التحقق من الحقول المطلوبة
        if not Validator.is_not_empty(data.get('code')):
            errors.append("كود المنتج مطلوب")
        elif not Validator.is_valid_product_code(data.get('code')):
            errors.append("كود المنتج غير صحيح")
        
        if not Validator.is_not_empty(data.get('name')):
            errors.append("اسم المنتج مطلوب")
        
        if not Validator.is_not_empty(data.get('unit')):
            errors.append("وحدة القياس مطلوبة")
        
        # التحقق من الأسعار
        cost_price = data.get('cost_price', 0)
        if not Validator.is_valid_number(cost_price, min_value=0):
            errors.append("سعر التكلفة يجب أن يكون رقم موجب")
        
        selling_price = data.get('selling_price', 0)
        if not Validator.is_valid_number(selling_price, min_value=0):
            errors.append("سعر البيع يجب أن يكون رقم موجب")
        
        # التحقق من الكميات
        min_quantity = data.get('min_quantity', 0)
        if not Validator.is_valid_integer(min_quantity, min_value=0):
            errors.append("الحد الأدنى للكمية يجب أن يكون رقم صحيح موجب")
        
        max_quantity = data.get('max_quantity', 1000)
        if not Validator.is_valid_integer(max_quantity, min_value=1):
            errors.append("الحد الأقصى للكمية يجب أن يكون رقم صحيح موجب")
        
        if min_quantity >= max_quantity:
            errors.append("الحد الأدنى يجب أن يكون أقل من الحد الأقصى")
        
        current_quantity = data.get('current_quantity', 0)
        if not Validator.is_valid_integer(current_quantity, min_value=0):
            errors.append("الكمية الحالية يجب أن تكون رقم صحيح موجب")
        
        # التحقق من الباركود (اختياري)
        barcode = data.get('barcode')
        if barcode and not Validator.is_valid_barcode(barcode):
            errors.append("الباركود غير صحيح")
        
        return errors

class SupplierValidator:
    """فئة للتحقق من صحة بيانات المورد"""
    
    @staticmethod
    def validate_supplier_data(data):
        """التحقق من صحة بيانات المورد"""
        errors = []
        
        # التحقق من الحقول المطلوبة
        if not Validator.is_not_empty(data.get('name')):
            errors.append("اسم المورد مطلوب")
        
        # التحقق من البريد الإلكتروني (اختياري)
        email = data.get('email')
        if email and not Validator.is_valid_email(email):
            errors.append("البريد الإلكتروني غير صحيح")
        
        # التحقق من رقم الهاتف (اختياري)
        phone = data.get('phone')
        if phone and not Validator.is_valid_phone(phone):
            errors.append("رقم الهاتف غير صحيح")
        
        # التحقق من الرقم الضريبي (اختياري)
        tax_number = data.get('tax_number')
        if tax_number and not Validator.is_valid_tax_number(tax_number):
            errors.append("الرقم الضريبي غير صحيح")
        
        return errors

class UserValidator:
    """فئة للتحقق من صحة بيانات المستخدم"""
    
    @staticmethod
    def validate_user_data(data):
        """التحقق من صحة بيانات المستخدم"""
        errors = []
        
        # التحقق من الحقول المطلوبة
        if not Validator.is_not_empty(data.get('username')):
            errors.append("اسم المستخدم مطلوب")
        elif not Validator.is_valid_username(data.get('username')):
            errors.append("اسم المستخدم غير صحيح")
        
        if not Validator.is_not_empty(data.get('full_name')):
            errors.append("الاسم الكامل مطلوب")
        
        # التحقق من كلمة المرور (للمستخدمين الجدد)
        if data.get('is_new_user', False):
            if not Validator.is_not_empty(data.get('password')):
                errors.append("كلمة المرور مطلوبة")
            elif not Validator.is_valid_password(data.get('password')):
                errors.append("كلمة المرور ضعيفة (يجب أن تكون 6 أحرف على الأقل)")
        
        # التحقق من الدور
        valid_roles = ['admin', 'manager', 'employee']
        if data.get('role') not in valid_roles:
            errors.append("دور المستخدم غير صحيح")
        
        return errors

class TransactionValidator:
    """فئة للتحقق من صحة بيانات المعاملة"""
    
    @staticmethod
    def validate_transaction_data(data):
        """التحقق من صحة بيانات المعاملة"""
        errors = []
        
        # التحقق من الحقول المطلوبة
        if not data.get('product_id'):
            errors.append("المنتج مطلوب")
        
        if not data.get('transaction_type'):
            errors.append("نوع المعاملة مطلوب")
        elif data.get('transaction_type') not in ['in', 'out', 'adjustment']:
            errors.append("نوع المعاملة غير صحيح")
        
        # التحقق من الكمية
        quantity = data.get('quantity', 0)
        if not Validator.is_valid_integer(quantity, min_value=1):
            errors.append("الكمية يجب أن تكون رقم صحيح موجب")
        
        # التحقق من السعر
        unit_price = data.get('unit_price', 0)
        if not Validator.is_valid_number(unit_price, min_value=0):
            errors.append("سعر الوحدة يجب أن يكون رقم موجب")
        
        return errors
