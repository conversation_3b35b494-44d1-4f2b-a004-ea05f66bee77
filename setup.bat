@echo off
chcp 65001 >nul
echo إعداد نظام إدارة المخازن...
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت على النظام
    echo يرجى تثبيت Python 3.8 أو أحدث من https://python.org
    pause
    exit /b 1
)

echo تثبيت المتطلبات الأساسية...
pip install customtkinter
if errorlevel 1 (
    echo خطأ في تثبيت customtkinter
    pause
    exit /b 1
)

echo.
echo تم تثبيت المتطلبات الأساسية بنجاح!
echo.

echo إنشاء البيانات التجريبية...
python reset_database.py
echo.

echo تشغيل البرنامج للمرة الأولى...
echo بيانات تسجيل الدخول:
echo - المدير: admin / admin123
echo - المشرف: manager1 / manager123
echo - الموظف: employee1 / emp123
echo.
python main.py

pause
