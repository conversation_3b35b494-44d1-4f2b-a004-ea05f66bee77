from config.database import db_manager
from datetime import datetime
from models.product import Product

class InventoryTransaction:
    def __init__(self, id=None, product_id=None, transaction_type=None, quantity=0,
                 unit_price=0, total_amount=0, reference_number=None, supplier_id=None,
                 user_id=None, notes=None, transaction_date=None):
        self.id = id
        self.product_id = product_id
        self.transaction_type = transaction_type  # 'in', 'out', 'adjustment'
        self.quantity = quantity
        self.unit_price = unit_price
        self.total_amount = total_amount
        self.reference_number = reference_number
        self.supplier_id = supplier_id
        self.user_id = user_id
        self.notes = notes
        self.transaction_date = transaction_date or datetime.now()
    
    def save(self):
        """حفظ المعاملة وتحديث كمية المنتج"""
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        # حفظ المعاملة
        if self.id:
            cursor.execute('''
                UPDATE inventory_transactions SET
                    product_id = ?, transaction_type = ?, quantity = ?, unit_price = ?,
                    total_amount = ?, reference_number = ?, supplier_id = ?, user_id = ?,
                    notes = ?, transaction_date = ?
                WHERE id = ?
            ''', (self.product_id, self.transaction_type, self.quantity, self.unit_price,
                  self.total_amount, self.reference_number, self.supplier_id, self.user_id,
                  self.notes, self.transaction_date, self.id))
        else:
            cursor.execute('''
                INSERT INTO inventory_transactions (
                    product_id, transaction_type, quantity, unit_price, total_amount,
                    reference_number, supplier_id, user_id, notes, transaction_date
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (self.product_id, self.transaction_type, self.quantity, self.unit_price,
                  self.total_amount, self.reference_number, self.supplier_id, self.user_id,
                  self.notes, self.transaction_date))
            
            self.id = cursor.lastrowid
        
        # تحديث كمية المنتج
        product = Product.get_by_id(self.product_id)
        if product:
            if self.transaction_type == 'in':
                new_quantity = product.current_quantity + self.quantity
            elif self.transaction_type == 'out':
                new_quantity = product.current_quantity - self.quantity
            else:  # adjustment
                new_quantity = self.quantity
            
            product.update_quantity(new_quantity)
        
        conn.commit()
        conn.close()
        return self.id
    
    @staticmethod
    def get_by_id(transaction_id):
        """الحصول على معاملة بالمعرف"""
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM inventory_transactions WHERE id = ?', (transaction_id,))
        row = cursor.fetchone()
        
        conn.close()
        
        if row:
            return InventoryTransaction(
                id=row['id'], product_id=row['product_id'],
                transaction_type=row['transaction_type'], quantity=row['quantity'],
                unit_price=row['unit_price'], total_amount=row['total_amount'],
                reference_number=row['reference_number'], supplier_id=row['supplier_id'],
                user_id=row['user_id'], notes=row['notes'],
                transaction_date=row['transaction_date']
            )
        return None
    
    @staticmethod
    def get_all(limit=None):
        """الحصول على جميع المعاملات"""
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        query = '''
            SELECT t.*, p.name as product_name, p.code as product_code,
                   s.name as supplier_name, u.full_name as user_name
            FROM inventory_transactions t
            LEFT JOIN products p ON t.product_id = p.id
            LEFT JOIN suppliers s ON t.supplier_id = s.id
            LEFT JOIN users u ON t.user_id = u.id
            ORDER BY t.transaction_date DESC
        '''
        
        if limit:
            query += f' LIMIT {limit}'
        
        cursor.execute(query)
        rows = cursor.fetchall()
        conn.close()
        
        transactions = []
        for row in rows:
            transaction = InventoryTransaction(
                id=row['id'], product_id=row['product_id'],
                transaction_type=row['transaction_type'], quantity=row['quantity'],
                unit_price=row['unit_price'], total_amount=row['total_amount'],
                reference_number=row['reference_number'], supplier_id=row['supplier_id'],
                user_id=row['user_id'], notes=row['notes'],
                transaction_date=row['transaction_date']
            )
            transaction.product_name = row['product_name']
            transaction.product_code = row['product_code']
            transaction.supplier_name = row['supplier_name']
            transaction.user_name = row['user_name']
            transactions.append(transaction)
        
        return transactions
    
    @staticmethod
    def get_by_product(product_id, limit=None):
        """الحصول على معاملات منتج معين"""
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        query = '''
            SELECT t.*, p.name as product_name, p.code as product_code,
                   s.name as supplier_name, u.full_name as user_name
            FROM inventory_transactions t
            LEFT JOIN products p ON t.product_id = p.id
            LEFT JOIN suppliers s ON t.supplier_id = s.id
            LEFT JOIN users u ON t.user_id = u.id
            WHERE t.product_id = ?
            ORDER BY t.transaction_date DESC
        '''
        
        if limit:
            query += f' LIMIT {limit}'
        
        cursor.execute(query, (product_id,))
        rows = cursor.fetchall()
        conn.close()
        
        transactions = []
        for row in rows:
            transaction = InventoryTransaction(
                id=row['id'], product_id=row['product_id'],
                transaction_type=row['transaction_type'], quantity=row['quantity'],
                unit_price=row['unit_price'], total_amount=row['total_amount'],
                reference_number=row['reference_number'], supplier_id=row['supplier_id'],
                user_id=row['user_id'], notes=row['notes'],
                transaction_date=row['transaction_date']
            )
            transaction.product_name = row['product_name']
            transaction.product_code = row['product_code']
            transaction.supplier_name = row['supplier_name']
            transaction.user_name = row['user_name']
            transactions.append(transaction)
        
        return transactions
    
    @staticmethod
    def get_by_date_range(start_date, end_date):
        """الحصول على معاملات في فترة زمنية"""
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT t.*, p.name as product_name, p.code as product_code,
                   s.name as supplier_name, u.full_name as user_name
            FROM inventory_transactions t
            LEFT JOIN products p ON t.product_id = p.id
            LEFT JOIN suppliers s ON t.supplier_id = s.id
            LEFT JOIN users u ON t.user_id = u.id
            WHERE DATE(t.transaction_date) BETWEEN ? AND ?
            ORDER BY t.transaction_date DESC
        ''', (start_date, end_date))
        
        rows = cursor.fetchall()
        conn.close()
        
        transactions = []
        for row in rows:
            transaction = InventoryTransaction(
                id=row['id'], product_id=row['product_id'],
                transaction_type=row['transaction_type'], quantity=row['quantity'],
                unit_price=row['unit_price'], total_amount=row['total_amount'],
                reference_number=row['reference_number'], supplier_id=row['supplier_id'],
                user_id=row['user_id'], notes=row['notes'],
                transaction_date=row['transaction_date']
            )
            transaction.product_name = row['product_name']
            transaction.product_code = row['product_code']
            transaction.supplier_name = row['supplier_name']
            transaction.user_name = row['user_name']
            transactions.append(transaction)
        
        return transactions

class PurchaseOrder:
    def __init__(self, id=None, order_number=None, supplier_id=None, order_date=None,
                 expected_date=None, status='pending', total_amount=0, notes=None, user_id=None):
        self.id = id
        self.order_number = order_number
        self.supplier_id = supplier_id
        self.order_date = order_date or datetime.now()
        self.expected_date = expected_date
        self.status = status  # 'pending', 'received', 'cancelled'
        self.total_amount = total_amount
        self.notes = notes
        self.user_id = user_id
        self.items = []
    
    def save(self):
        """حفظ أمر التوريد"""
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        if self.id:
            cursor.execute('''
                UPDATE purchase_orders SET
                    order_number = ?, supplier_id = ?, order_date = ?, expected_date = ?,
                    status = ?, total_amount = ?, notes = ?, user_id = ?
                WHERE id = ?
            ''', (self.order_number, self.supplier_id, self.order_date, self.expected_date,
                  self.status, self.total_amount, self.notes, self.user_id, self.id))
        else:
            cursor.execute('''
                INSERT INTO purchase_orders (
                    order_number, supplier_id, order_date, expected_date,
                    status, total_amount, notes, user_id
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (self.order_number, self.supplier_id, self.order_date, self.expected_date,
                  self.status, self.total_amount, self.notes, self.user_id))
            
            self.id = cursor.lastrowid
        
        conn.commit()
        conn.close()
        return self.id
    
    @staticmethod
    def get_all():
        """الحصول على جميع أوامر التوريد"""
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT po.*, s.name as supplier_name, u.full_name as user_name
            FROM purchase_orders po
            LEFT JOIN suppliers s ON po.supplier_id = s.id
            LEFT JOIN users u ON po.user_id = u.id
            ORDER BY po.order_date DESC
        ''')
        
        rows = cursor.fetchall()
        conn.close()
        
        orders = []
        for row in rows:
            order = PurchaseOrder(
                id=row['id'], order_number=row['order_number'],
                supplier_id=row['supplier_id'], order_date=row['order_date'],
                expected_date=row['expected_date'], status=row['status'],
                total_amount=row['total_amount'], notes=row['notes'],
                user_id=row['user_id']
            )
            order.supplier_name = row['supplier_name']
            order.user_name = row['user_name']
            orders.append(order)
        
        return orders

class Customer:
    """نموذج العملاء"""
    def __init__(self, id=None, name=None, contact_person=None, phone=None,
                 email=None, address=None, tax_number=None, customer_type='individual',
                 is_active=True):
        self.id = id
        self.name = name
        self.contact_person = contact_person
        self.phone = phone
        self.email = email
        self.address = address
        self.tax_number = tax_number
        self.customer_type = customer_type  # 'individual', 'company'
        self.is_active = is_active

    def save(self):
        """حفظ العميل في قاعدة البيانات"""
        conn = db_manager.get_connection()
        cursor = conn.cursor()

        # إنشاء جدول العملاء إذا لم يكن موجوداً
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                contact_person TEXT,
                phone TEXT,
                email TEXT,
                address TEXT,
                tax_number TEXT,
                customer_type TEXT DEFAULT 'individual',
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        if self.id:
            # تحديث عميل موجود
            cursor.execute('''
                UPDATE customers SET
                    name = ?, contact_person = ?, phone = ?, email = ?,
                    address = ?, tax_number = ?, customer_type = ?, is_active = ?
                WHERE id = ?
            ''', (self.name, self.contact_person, self.phone, self.email,
                  self.address, self.tax_number, self.customer_type, self.is_active, self.id))
        else:
            # إضافة عميل جديد
            cursor.execute('''
                INSERT INTO customers (
                    name, contact_person, phone, email, address, tax_number, customer_type, is_active
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (self.name, self.contact_person, self.phone, self.email,
                  self.address, self.tax_number, self.customer_type, self.is_active))

            self.id = cursor.lastrowid

        conn.commit()
        conn.close()
        return self.id

    @staticmethod
    def get_all(active_only=True):
        """الحصول على جميع العملاء"""
        conn = db_manager.get_connection()
        cursor = conn.cursor()

        # إنشاء الجدول إذا لم يكن موجوداً
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                contact_person TEXT,
                phone TEXT,
                email TEXT,
                address TEXT,
                tax_number TEXT,
                customer_type TEXT DEFAULT 'individual',
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        if active_only:
            cursor.execute('SELECT * FROM customers WHERE is_active = 1 ORDER BY name')
        else:
            cursor.execute('SELECT * FROM customers ORDER BY name')

        rows = cursor.fetchall()
        conn.close()

        customers = []
        for row in rows:
            customers.append(Customer(
                id=row['id'], name=row['name'], contact_person=row['contact_person'],
                phone=row['phone'], email=row['email'], address=row['address'],
                tax_number=row['tax_number'], customer_type=row['customer_type'],
                is_active=row['is_active']
            ))

        return customers

    @staticmethod
    def get_by_id(customer_id):
        """الحصول على عميل بالمعرف"""
        conn = db_manager.get_connection()
        cursor = conn.cursor()

        cursor.execute('SELECT * FROM customers WHERE id = ?', (customer_id,))
        row = cursor.fetchone()

        conn.close()

        if row:
            return Customer(
                id=row['id'], name=row['name'], contact_person=row['contact_person'],
                phone=row['phone'], email=row['email'], address=row['address'],
                tax_number=row['tax_number'], customer_type=row['customer_type'],
                is_active=row['is_active']
            )
        return None

class SalesOrder:
    """نموذج أوامر البيع"""
    def __init__(self, id=None, order_number=None, customer_id=None, order_date=None,
                 delivery_date=None, status='pending', total_amount=0, discount=0,
                 tax_amount=0, final_amount=0, notes=None, user_id=None):
        self.id = id
        self.order_number = order_number
        self.customer_id = customer_id
        self.order_date = order_date or datetime.now()
        self.delivery_date = delivery_date
        self.status = status  # 'pending', 'confirmed', 'delivered', 'cancelled'
        self.total_amount = total_amount
        self.discount = discount
        self.tax_amount = tax_amount
        self.final_amount = final_amount
        self.notes = notes
        self.user_id = user_id
        self.items = []

    def save(self):
        """حفظ أمر البيع"""
        conn = db_manager.get_connection()
        cursor = conn.cursor()

        # إنشاء جدول أوامر البيع إذا لم يكن موجوداً
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sales_orders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_number TEXT UNIQUE NOT NULL,
                customer_id INTEGER,
                order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                delivery_date DATE,
                status TEXT DEFAULT 'pending',
                total_amount REAL DEFAULT 0,
                discount REAL DEFAULT 0,
                tax_amount REAL DEFAULT 0,
                final_amount REAL DEFAULT 0,
                notes TEXT,
                user_id INTEGER,
                FOREIGN KEY (customer_id) REFERENCES customers (id),
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')

        # إنشاء جدول تفاصيل أوامر البيع
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sales_order_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_id INTEGER NOT NULL,
                product_id INTEGER NOT NULL,
                quantity INTEGER NOT NULL,
                unit_price REAL NOT NULL,
                total_price REAL NOT NULL,
                delivered_quantity INTEGER DEFAULT 0,
                FOREIGN KEY (order_id) REFERENCES sales_orders (id),
                FOREIGN KEY (product_id) REFERENCES products (id)
            )
        ''')

        if self.id:
            cursor.execute('''
                UPDATE sales_orders SET
                    order_number = ?, customer_id = ?, order_date = ?, delivery_date = ?,
                    status = ?, total_amount = ?, discount = ?, tax_amount = ?,
                    final_amount = ?, notes = ?, user_id = ?
                WHERE id = ?
            ''', (self.order_number, self.customer_id, self.order_date, self.delivery_date,
                  self.status, self.total_amount, self.discount, self.tax_amount,
                  self.final_amount, self.notes, self.user_id, self.id))
        else:
            cursor.execute('''
                INSERT INTO sales_orders (
                    order_number, customer_id, order_date, delivery_date,
                    status, total_amount, discount, tax_amount, final_amount, notes, user_id
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (self.order_number, self.customer_id, self.order_date, self.delivery_date,
                  self.status, self.total_amount, self.discount, self.tax_amount,
                  self.final_amount, self.notes, self.user_id))

            self.id = cursor.lastrowid

        conn.commit()
        conn.close()
        return self.id

    @staticmethod
    def get_all():
        """الحصول على جميع أوامر البيع"""
        conn = db_manager.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT so.*, c.name as customer_name, u.full_name as user_name
            FROM sales_orders so
            LEFT JOIN customers c ON so.customer_id = c.id
            LEFT JOIN users u ON so.user_id = u.id
            ORDER BY so.order_date DESC
        ''')

        rows = cursor.fetchall()
        conn.close()

        orders = []
        for row in rows:
            order = SalesOrder(
                id=row['id'], order_number=row['order_number'],
                customer_id=row['customer_id'], order_date=row['order_date'],
                delivery_date=row['delivery_date'], status=row['status'],
                total_amount=row['total_amount'], discount=row['discount'],
                tax_amount=row['tax_amount'], final_amount=row['final_amount'],
                notes=row['notes'], user_id=row['user_id']
            )
            order.customer_name = row['customer_name']
            order.user_name = row['user_name']
            orders.append(order)

        return orders
