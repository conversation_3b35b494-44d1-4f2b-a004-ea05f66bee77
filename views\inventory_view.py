import customtkinter as ctk
import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime

from models.product import Product
from models.transaction import InventoryTransaction
from models.user import User

class InventoryView:
    def __init__(self, parent, main_window):
        self.parent = parent
        self.main_window = main_window
        self.current_user = main_window.current_user
        self.setup_ui()
        self.load_inventory()
    
    def setup_ui(self):
        """إعداد واجهة إدارة المخزون"""
        # إطار رئيسي
        self.main_frame = ctk.CTkFrame(self.parent)
        self.main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # عنوان
        title = ctk.CTkLabel(
            self.main_frame,
            text="إدارة المخزون",
            font=("Arial", 20, "bold")
        )
        title.pack(pady=10)
        
        # إطار الأزرار العلوية
        buttons_frame = ctk.CTkFrame(self.main_frame)
        buttons_frame.pack(fill="x", padx=10, pady=5)
        
        # أزرار التحكم
        add_stock_btn = ctk.CTkButton(
            buttons_frame,
            text="➕ إضافة مخزون",
            command=self.add_stock,
            width=120
        )
        add_stock_btn.pack(side="right", padx=5, pady=10)
        
        remove_stock_btn = ctk.CTkButton(
            buttons_frame,
            text="➖ صرف مخزون",
            command=self.remove_stock,
            width=120
        )
        remove_stock_btn.pack(side="right", padx=5, pady=10)
        
        adjust_stock_btn = ctk.CTkButton(
            buttons_frame,
            text="🔧 تعديل مخزون",
            command=self.adjust_stock,
            width=120,
            fg_color="orange"
        )
        adjust_stock_btn.pack(side="right", padx=5, pady=10)
        
        refresh_btn = ctk.CTkButton(
            buttons_frame,
            text="🔄 تحديث",
            command=self.load_inventory,
            width=100
        )
        refresh_btn.pack(side="left", padx=5, pady=10)
        
        # إطار البحث والفلترة
        filter_frame = ctk.CTkFrame(buttons_frame)
        filter_frame.pack(side="left", padx=10, pady=10)
        
        # فلتر حالة المخزون
        filter_label = ctk.CTkLabel(filter_frame, text="عرض:")
        filter_label.pack(side="right", padx=5)
        
        self.filter_var = ctk.StringVar(value="الكل")
        filter_options = ["الكل", "مخزون منخفض", "مخزون عادي", "مخزون مرتفع", "نفد المخزون"]
        self.filter_combo = ctk.CTkComboBox(
            filter_frame,
            values=filter_options,
            variable=self.filter_var,
            command=self.filter_inventory,
            width=150
        )
        self.filter_combo.pack(side="right", padx=5)
        
        # إطار المحتوى الرئيسي
        content_frame = ctk.CTkFrame(self.main_frame)
        content_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        # جدول المخزون
        self.create_inventory_table(content_frame)
        
        # إطار الإحصائيات
        self.create_stats_frame()
    
    def create_inventory_table(self, parent):
        """إنشاء جدول المخزون"""
        # إطار الجدول
        table_frame = ctk.CTkFrame(parent)
        table_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # أعمدة الجدول
        columns = ("الكود", "اسم المنتج", "التصنيف", "الوحدة", "الكمية الحالية", 
                  "الحد الأدنى", "الحد الأقصى", "حالة المخزون", "آخر حركة")
        
        self.inventory_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=12)
        
        # تعيين عناوين الأعمدة وعرضها
        column_widths = [80, 150, 100, 80, 100, 80, 80, 120, 120]
        for i, col in enumerate(columns):
            self.inventory_tree.heading(col, text=col)
            self.inventory_tree.column(col, width=column_widths[i], anchor="center")
        
        # شريط التمرير
        scrollbar_y = ttk.Scrollbar(table_frame, orient="vertical", command=self.inventory_tree.yview)
        scrollbar_x = ttk.Scrollbar(table_frame, orient="horizontal", command=self.inventory_tree.xview)
        
        self.inventory_tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
        
        # تخطيط الجدول
        self.inventory_tree.pack(side="right", fill="both", expand=True)
        scrollbar_y.pack(side="left", fill="y")
        scrollbar_x.pack(side="bottom", fill="x")
        
        # ربط النقر المزدوج بعرض تفاصيل المنتج
        self.inventory_tree.bind("<Double-1>", lambda e: self.show_product_details())
    
    def create_stats_frame(self):
        """إنشاء إطار الإحصائيات"""
        stats_frame = ctk.CTkFrame(self.main_frame)
        stats_frame.pack(fill="x", padx=10, pady=5)
        
        # إحصائيات المخزون
        self.total_products_label = ctk.CTkLabel(stats_frame, text="إجمالي المنتجات: 0")
        self.total_products_label.pack(side="right", padx=10, pady=5)
        
        self.low_stock_label = ctk.CTkLabel(stats_frame, text="مخزون منخفض: 0", text_color="red")
        self.low_stock_label.pack(side="right", padx=10, pady=5)
        
        self.out_of_stock_label = ctk.CTkLabel(stats_frame, text="نفد المخزون: 0", text_color="red")
        self.out_of_stock_label.pack(side="right", padx=10, pady=5)
        
        self.total_value_label = ctk.CTkLabel(stats_frame, text="قيمة المخزون: 0 ريال")
        self.total_value_label.pack(side="left", padx=10, pady=5)
    
    def load_inventory(self):
        """تحميل بيانات المخزون"""
        # مسح البيانات الحالية
        for item in self.inventory_tree.get_children():
            self.inventory_tree.delete(item)
        
        # تحميل المنتجات
        products = Product.get_all()
        
        total_products = 0
        low_stock_count = 0
        out_of_stock_count = 0
        total_value = 0
        
        for product in products:
            # تحديد حالة المخزون
            if product.current_quantity == 0:
                stock_status = "نفد المخزون"
                status_color = "red"
                out_of_stock_count += 1
            elif product.current_quantity <= product.min_quantity:
                stock_status = "مخزون منخفض"
                status_color = "orange"
                low_stock_count += 1
            elif product.current_quantity >= product.max_quantity:
                stock_status = "مخزون مرتفع"
                status_color = "blue"
            else:
                stock_status = "مخزون عادي"
                status_color = "green"
            
            # الحصول على آخر حركة
            last_transaction = self.get_last_transaction(product.id)
            last_movement = last_transaction.strftime("%Y-%m-%d") if last_transaction else "لا توجد"
            
            # إدراج البيانات في الجدول
            item_id = self.inventory_tree.insert("", "end", values=(
                product.code,
                product.name,
                getattr(product, 'category_name', '') or '',
                product.unit,
                product.current_quantity,
                product.min_quantity,
                product.max_quantity,
                stock_status,
                last_movement
            ))
            
            # تلوين الصف حسب حالة المخزون
            if stock_status == "نفد المخزون":
                self.inventory_tree.set(item_id, "حالة المخزون", stock_status)
            elif stock_status == "مخزون منخفض":
                self.inventory_tree.set(item_id, "حالة المخزون", stock_status)
            
            total_products += 1
            total_value += product.current_quantity * product.cost_price
        
        # تحديث الإحصائيات
        self.update_stats(total_products, low_stock_count, out_of_stock_count, total_value)
    
    def get_last_transaction(self, product_id):
        """الحصول على تاريخ آخر معاملة للمنتج"""
        transactions = InventoryTransaction.get_by_product(product_id, limit=1)
        if transactions:
            return transactions[0].transaction_date
        return None
    
    def update_stats(self, total_products, low_stock, out_of_stock, total_value):
        """تحديث الإحصائيات"""
        self.total_products_label.configure(text=f"إجمالي المنتجات: {total_products}")
        self.low_stock_label.configure(text=f"مخزون منخفض: {low_stock}")
        self.out_of_stock_label.configure(text=f"نفد المخزون: {out_of_stock}")
        self.total_value_label.configure(text=f"قيمة المخزون: {total_value:,.2f} ريال")
    
    def filter_inventory(self, selected_filter=None):
        """فلترة المخزون حسب الحالة"""
        filter_value = self.filter_var.get()
        
        # مسح البيانات الحالية
        for item in self.inventory_tree.get_children():
            self.inventory_tree.delete(item)
        
        # تحميل المنتجات مع الفلترة
        products = Product.get_all()
        
        for product in products:
            # تحديد حالة المخزون
            if product.current_quantity == 0:
                stock_status = "نفد المخزون"
            elif product.current_quantity <= product.min_quantity:
                stock_status = "مخزون منخفض"
            elif product.current_quantity >= product.max_quantity:
                stock_status = "مخزون مرتفع"
            else:
                stock_status = "مخزون عادي"
            
            # تطبيق الفلتر
            if filter_value == "الكل" or filter_value == stock_status:
                last_transaction = self.get_last_transaction(product.id)
                last_movement = last_transaction.strftime("%Y-%m-%d") if last_transaction else "لا توجد"
                
                self.inventory_tree.insert("", "end", values=(
                    product.code,
                    product.name,
                    getattr(product, 'category_name', '') or '',
                    product.unit,
                    product.current_quantity,
                    product.min_quantity,
                    product.max_quantity,
                    stock_status,
                    last_movement
                ))
    
    def get_selected_product(self):
        """الحصول على المنتج المحدد"""
        selection = self.inventory_tree.selection()
        if not selection:
            return None
        
        item = self.inventory_tree.item(selection[0])
        product_code = item['values'][0]
        
        return Product.get_by_code(product_code)
    
    def add_stock(self):
        """إضافة مخزون"""
        selected_product = self.get_selected_product()
        if not selected_product:
            messagebox.showwarning("تحذير", "يرجى اختيار منتج لإضافة المخزون")
            return
        
        self.show_transaction_form(selected_product, "in")
    
    def remove_stock(self):
        """صرف مخزون"""
        selected_product = self.get_selected_product()
        if not selected_product:
            messagebox.showwarning("تحذير", "يرجى اختيار منتج لصرف المخزون")
            return
        
        if selected_product.current_quantity <= 0:
            messagebox.showerror("خطأ", "لا يمكن صرف من منتج نفد مخزونه")
            return
        
        self.show_transaction_form(selected_product, "out")
    
    def adjust_stock(self):
        """تعديل مخزون"""
        selected_product = self.get_selected_product()
        if not selected_product:
            messagebox.showwarning("تحذير", "يرجى اختيار منتج لتعديل المخزون")
            return
        
        self.show_transaction_form(selected_product, "adjustment")
    
    def show_transaction_form(self, product, transaction_type):
        """عرض نموذج المعاملة"""
        # إنشاء نافذة جديدة
        form_window = ctk.CTkToplevel(self.parent)
        
        titles = {
            "in": "إضافة مخزون",
            "out": "صرف مخزون", 
            "adjustment": "تعديل مخزون"
        }
        
        form_window.title(titles[transaction_type])
        form_window.geometry("500x400")
        form_window.transient(self.parent)
        form_window.grab_set()
        
        # إطار رئيسي
        main_frame = ctk.CTkFrame(form_window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # عنوان
        title = ctk.CTkLabel(
            main_frame,
            text=titles[transaction_type],
            font=("Arial", 18, "bold")
        )
        title.pack(pady=10)
        
        # معلومات المنتج
        product_frame = ctk.CTkFrame(main_frame)
        product_frame.pack(fill="x", pady=10)
        
        ctk.CTkLabel(product_frame, text=f"المنتج: {product.name}", font=("Arial", 12, "bold")).pack(pady=5)
        ctk.CTkLabel(product_frame, text=f"الكود: {product.code}").pack()
        ctk.CTkLabel(product_frame, text=f"الكمية الحالية: {product.current_quantity} {product.unit}").pack()
        
        # حقول النموذج
        fields = {}
        
        # الكمية
        quantity_frame = ctk.CTkFrame(main_frame)
        quantity_frame.pack(fill="x", pady=5)
        
        if transaction_type == "adjustment":
            ctk.CTkLabel(quantity_frame, text="الكمية الجديدة *", width=120).pack(side="right", padx=5)
        else:
            ctk.CTkLabel(quantity_frame, text="الكمية *", width=120).pack(side="right", padx=5)
        
        fields['quantity'] = ctk.CTkEntry(quantity_frame, width=200)
        fields['quantity'].pack(side="left", padx=5)
        
        # السعر (للوارد والصادر)
        if transaction_type != "adjustment":
            price_frame = ctk.CTkFrame(main_frame)
            price_frame.pack(fill="x", pady=5)
            
            price_label = "سعر الوحدة *" if transaction_type == "in" else "سعر البيع *"
            ctk.CTkLabel(price_frame, text=price_label, width=120).pack(side="right", padx=5)
            
            fields['unit_price'] = ctk.CTkEntry(price_frame, width=200)
            fields['unit_price'].pack(side="left", padx=5)
            
            # تعيين السعر الافتراضي
            default_price = product.cost_price if transaction_type == "in" else product.selling_price
            fields['unit_price'].insert(0, str(default_price))
        
        # رقم المرجع
        ref_frame = ctk.CTkFrame(main_frame)
        ref_frame.pack(fill="x", pady=5)
        
        ctk.CTkLabel(ref_frame, text="رقم المرجع", width=120).pack(side="right", padx=5)
        fields['reference'] = ctk.CTkEntry(ref_frame, width=200)
        fields['reference'].pack(side="left", padx=5)
        
        # الملاحظات
        notes_frame = ctk.CTkFrame(main_frame)
        notes_frame.pack(fill="x", pady=5)
        
        ctk.CTkLabel(notes_frame, text="الملاحظات", width=120).pack(side="right", padx=5)
        fields['notes'] = ctk.CTkTextbox(notes_frame, width=200, height=60)
        fields['notes'].pack(side="left", padx=5)
        
        # أزرار الحفظ والإلغاء
        buttons_frame = ctk.CTkFrame(main_frame)
        buttons_frame.pack(fill="x", pady=20)
        
        save_btn = ctk.CTkButton(
            buttons_frame,
            text="💾 حفظ",
            command=lambda: self.save_transaction(fields, product, transaction_type, form_window),
            width=120
        )
        save_btn.pack(side="left", padx=10)
        
        cancel_btn = ctk.CTkButton(
            buttons_frame,
            text="❌ إلغاء",
            command=form_window.destroy,
            width=120,
            fg_color="gray"
        )
        cancel_btn.pack(side="right", padx=10)
    
    def save_transaction(self, fields, product, transaction_type, form_window):
        """حفظ المعاملة"""
        try:
            # جمع البيانات
            quantity = int(fields['quantity'].get() or 0)
            unit_price = float(fields.get('unit_price', {}).get() or 0) if 'unit_price' in fields else 0
            reference = fields['reference'].get().strip()
            notes = fields['notes'].get("1.0", "end-1c").strip()
            
            # التحقق من صحة البيانات
            if quantity <= 0:
                messagebox.showerror("خطأ", "يرجى إدخال كمية صحيحة")
                return
            
            if transaction_type == "out" and quantity > product.current_quantity:
                messagebox.showerror("خطأ", "الكمية المطلوبة أكبر من المتوفر في المخزون")
                return
            
            # إنشاء المعاملة
            transaction = InventoryTransaction(
                product_id=product.id,
                transaction_type=transaction_type,
                quantity=quantity,
                unit_price=unit_price,
                total_amount=quantity * unit_price,
                reference_number=reference,
                user_id=self.current_user.id,
                notes=notes,
                transaction_date=datetime.now()
            )
            
            transaction.save()
            
            # إغلاق النموذج وتحديث الجدول
            form_window.destroy()
            self.load_inventory()
            
            messagebox.showinfo("نجح", "تم حفظ المعاملة بنجاح")
            
        except ValueError:
            messagebox.showerror("خطأ", "يرجى التأكد من صحة البيانات المدخلة")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ المعاملة:\n{str(e)}")
    
    def show_product_details(self):
        """عرض تفاصيل المنتج"""
        selected_product = self.get_selected_product()
        if not selected_product:
            return
        
        # إنشاء نافذة التفاصيل
        details_window = ctk.CTkToplevel(self.parent)
        details_window.title(f"تفاصيل المنتج - {selected_product.name}")
        details_window.geometry("600x500")
        details_window.transient(self.parent)
        
        # إطار رئيسي
        main_frame = ctk.CTkScrollableFrame(details_window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # معلومات المنتج
        info_frame = ctk.CTkFrame(main_frame)
        info_frame.pack(fill="x", pady=10)
        
        ctk.CTkLabel(info_frame, text=f"اسم المنتج: {selected_product.name}", font=("Arial", 14, "bold")).pack(anchor="w", padx=10, pady=5)
        ctk.CTkLabel(info_frame, text=f"الكود: {selected_product.code}").pack(anchor="w", padx=10, pady=2)
        ctk.CTkLabel(info_frame, text=f"الوصف: {selected_product.description or 'لا يوجد'}").pack(anchor="w", padx=10, pady=2)
        ctk.CTkLabel(info_frame, text=f"الوحدة: {selected_product.unit}").pack(anchor="w", padx=10, pady=2)
        ctk.CTkLabel(info_frame, text=f"الكمية الحالية: {selected_product.current_quantity}").pack(anchor="w", padx=10, pady=2)
        ctk.CTkLabel(info_frame, text=f"الحد الأدنى: {selected_product.min_quantity}").pack(anchor="w", padx=10, pady=2)
        ctk.CTkLabel(info_frame, text=f"الحد الأقصى: {selected_product.max_quantity}").pack(anchor="w", padx=10, pady=2)
        
        # آخر المعاملات
        transactions_frame = ctk.CTkFrame(main_frame)
        transactions_frame.pack(fill="both", expand=True, pady=10)
        
        ctk.CTkLabel(transactions_frame, text="آخر المعاملات", font=("Arial", 14, "bold")).pack(pady=10)
        
        # جدول المعاملات
        columns = ("التاريخ", "النوع", "الكمية", "السعر", "المستخدم")
        transactions_tree = ttk.Treeview(transactions_frame, columns=columns, show="headings", height=8)
        
        for col in columns:
            transactions_tree.heading(col, text=col)
            transactions_tree.column(col, width=100, anchor="center")
        
        # تحميل المعاملات
        transactions = InventoryTransaction.get_by_product(selected_product.id, limit=10)
        for transaction in transactions:
            transaction_type_ar = {
                'in': 'وارد',
                'out': 'صادر',
                'adjustment': 'تعديل'
            }.get(transaction.transaction_type, transaction.transaction_type)
            
            transactions_tree.insert("", "end", values=(
                transaction.transaction_date.strftime("%Y-%m-%d %H:%M") if transaction.transaction_date else "",
                transaction_type_ar,
                transaction.quantity,
                f"{transaction.unit_price:.2f}",
                transaction.user_name or ""
            ))
        
        transactions_tree.pack(fill="both", expand=True, padx=10, pady=10)
