# نظام إدارة المخازن 📦

نظام شامل لإدارة المخازن والمنتجات والموردين مطور بلغة Python مع واجهة مستخدم حديثة ومرنة.

## المميزات الرئيسية ✨

### 📦 1. إدارة الأصناف (المنتجات)
- تصنيف المنتجات (أثاث، خامات، ملحقات...)
- إضافة/تعديل/حذف صنف
- كود الصنف/الباركود
- وصف المنتج وصوره
- كمية الحد الأدنى/الأقصى للمخزون

### 🚚 2. إدارة الواردات (المشتريات)
- تسجيل أوامر التوريد
- استقبال الأصناف من الموردين
- فواتير الشراء
- تتبع تواريخ التوريد والكميات

### 📤 3. إدارة الصادرات (المبيعات أو الصرف الداخلي)
- صرف الأصناف (للبيع أو للاستخدام الداخلي)
- أوامر الصرف
- الربط مع إدارة المبيعات

### 🔁 4. حركات المخزون
- سجل الحركات (وارد/صادر/مرتجع)
- البحث حسب صنف أو تاريخ
- طباعة أو تصدير تقرير حركة

### 🧾 5. الجرد وإدارة المخزون
- تقارير المخزون الحالي
- مقارنة الكميات الفعلية بالنظام
- إدارة عمليات الجرد الدوري
- تنبيهات بنقص المخزون

### 🧑‍💼 6. الموردين
- بيانات الموردين
- أرشيف فواتير المورد
- سجل التعاملات السابقة

### 👥 7. المستخدمين والصلاحيات
- إدارة الحسابات
- تحديد صلاحيات (مدير، مشرف، موظف)
- تتبع العمليات حسب المستخدم

### 📊 8. التقارير والإحصائيات
- تقارير شهرية/سنوية للمخزون
- تقارير المنتجات الأكثر حركة
- تقارير الموردين

### ⚙️ 9. الإعدادات العامة
- إعدادات النظام (لغة، عملة...)
- إعدادات الطباعة والتقارير
- إعدادات إشعارات المخزون

### 🏠 10. الواجهة الرئيسية (Dashboard)
- ملخص سريع للمخزون الحالي
- التنبيهات (كميات منخفضة، منتجات منتهية)
- اختصارات للوحدات المهمة

## التقنيات المستخدمة 🛠️

- **Python 3.8+** - لغة البرمجة الأساسية
- **CustomTkinter** - واجهة المستخدم الحديثة
- **SQLite** - قاعدة البيانات المحلية
- **Pillow** - معالجة الصور
- **ReportLab** - إنشاء تقارير PDF
- **Matplotlib** - الرسوم البيانية والإحصائيات
- **python-barcode** - إنشاء الباركود
- **qrcode** - إنشاء QR Code
- **openpyxl** - تصدير البيانات إلى Excel

## متطلبات التشغيل 📋

- Python 3.8 أو أحدث
- نظام التشغيل: Windows, macOS, Linux

## التثبيت والتشغيل 🚀

### 1. تحميل المشروع
```bash
git clone [repository-url]
cd warehouse_management
```

### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

أو تثبيت المكتبات يدوياً:
```bash
pip install customtkinter pillow reportlab matplotlib python-barcode qrcode openpyxl tkcalendar
```

### 3. تشغيل البرنامج
```bash
python main.py
```

## بيانات تسجيل الدخول الافتراضية 🔐

- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

## هيكل المشروع 📁

```
warehouse_management/
├── main.py                 # نقطة البداية
├── requirements.txt        # المتطلبات
├── README.md              # دليل الاستخدام
├── config/                # إعدادات النظام
│   ├── database.py        # إعداد قاعدة البيانات
│   └── settings.py        # الإعدادات العامة
├── models/                # نماذج البيانات
│   ├── product.py         # نموذج المنتجات
│   ├── supplier.py        # نموذج الموردين
│   ├── transaction.py     # نموذج المعاملات
│   └── user.py           # نموذج المستخدمين
├── views/                 # واجهات المستخدم
│   ├── main_window.py     # النافذة الرئيسية
│   ├── products_view.py   # واجهة المنتجات
│   ├── inventory_view.py  # واجهة المخزون
│   ├── suppliers_view.py  # واجهة الموردين
│   ├── reports_view.py    # واجهة التقارير
│   └── settings_view.py   # واجهة الإعدادات
├── controllers/           # منطق التحكم
├── utils/                 # الأدوات المساعدة
│   ├── helpers.py         # دوال مساعدة
│   └── validators.py      # التحقق من البيانات
├── assets/               # الأصول
│   ├── icons/            # الأيقونات
│   └── images/           # الصور
└── data/                 # البيانات
    └── warehouse.db      # قاعدة البيانات
```

## الاستخدام 📖

### تسجيل الدخول
1. شغل البرنامج باستخدام `python main.py`
2. أدخل بيانات تسجيل الدخول الافتراضية
3. اضغط "تسجيل الدخول"

### إدارة المنتجات
1. اختر "إدارة المنتجات" من القائمة الجانبية
2. أضف منتجات جديدة أو عدل المنتجات الموجودة
3. حدد التصنيفات والأسعار والكميات

### إدارة المخزون
1. اختر "المخزون" لعرض حالة المخزون الحالية
2. راقب التنبيهات للمنتجات منخفضة المخزون
3. قم بعمليات الجرد الدورية

### إدارة الموردين
1. اختر "الموردين" لإدارة بيانات الموردين
2. أضف موردين جدد أو عدل بيانات الموردين الحاليين
3. تتبع المعاملات مع كل مورد

### التقارير
1. اختر "التقارير" لعرض التقارير والإحصائيات
2. اختر نوع التقرير والفترة الزمنية
3. صدر التقارير إلى PDF أو Excel

## الصلاحيات 👤

### مدير النظام (Admin)
- جميع الصلاحيات
- إدارة المستخدمين
- إعدادات النظام

### مشرف (Manager)
- إدارة المنتجات والموردين
- عرض التقارير
- إدارة المخزون

### موظف (Employee)
- عرض المخزون
- إضافة معاملات المخزون

## النسخ الاحتياطية 💾

يقوم النظام بإنشاء نسخ احتياطية تلقائية من قاعدة البيانات في مجلد `backup/`

## الدعم والمساعدة 🆘

للحصول على المساعدة أو الإبلاغ عن مشاكل:
1. تحقق من ملف README
2. راجع رسائل الخطأ في وحدة التحكم
3. تأكد من تثبيت جميع المتطلبات بشكل صحيح

## الترخيص 📄

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## المساهمة 🤝

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع للميزة الجديدة
3. إرسال Pull Request

## التطوير المستقبلي 🔮

- [ ] واجهة ويب
- [ ] تطبيق موبايل
- [ ] تكامل مع أنظمة المحاسبة
- [ ] دعم قواعد بيانات متقدمة
- [ ] تقارير متقدمة مع الذكاء الاصطناعي
- [ ] نظام إشعارات متقدم
- [ ] دعم متعدد اللغات
- [ ] تكامل مع أنظمة نقاط البيع

---

**تم تطوير هذا النظام بواسطة Augment Agent** 🤖
