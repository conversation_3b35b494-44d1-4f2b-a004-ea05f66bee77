import customtkinter as ctk
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
from datetime import datetime

from config.settings import app_settings
from utils.helpers import backup_database, restore_database

class SettingsView:
    def __init__(self, parent, main_window):
        self.parent = parent
        self.main_window = main_window
        self.current_user = main_window.current_user
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة الإعدادات"""
        # إطار رئيسي
        self.main_frame = ctk.CTkFrame(self.parent)
        self.main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # عنوان
        title = ctk.CTkLabel(
            self.main_frame,
            text="إعدادات النظام",
            font=("Arial", 20, "bold")
        )
        title.pack(pady=10)
        
        # إطار التبويبات
        self.notebook = ttk.Notebook(self.main_frame)
        self.notebook.pack(fill="both", expand=True, padx=10, pady=10)
        
        # تبويب الإعدادات العامة
        self.create_general_tab()
        
        # تبويب النسخ الاحتياطية
        self.create_backup_tab()
        
        # تبويب معلومات النظام
        self.create_info_tab()
    
    def create_general_tab(self):
        """إنشاء تبويب الإعدادات العامة"""
        general_frame = ctk.CTkFrame(self.notebook)
        self.notebook.add(general_frame, text="الإعدادات العامة")
        
        # إطار المحتوى
        content_frame = ctk.CTkScrollableFrame(general_frame)
        content_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # إعدادات الشركة
        company_section = ctk.CTkFrame(content_frame)
        company_section.pack(fill="x", pady=10)
        
        ctk.CTkLabel(
            company_section,
            text="معلومات الشركة",
            font=("Arial", 16, "bold")
        ).pack(pady=10)
        
        # اسم الشركة
        company_name_frame = ctk.CTkFrame(company_section)
        company_name_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkLabel(company_name_frame, text="اسم الشركة:", width=120).pack(side="right", padx=5)
        self.company_name_entry = ctk.CTkEntry(company_name_frame, width=300)
        self.company_name_entry.pack(side="left", padx=5)
        self.company_name_entry.insert(0, app_settings.get('company_name', ''))
        
        # العملة
        currency_frame = ctk.CTkFrame(company_section)
        currency_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkLabel(currency_frame, text="العملة:", width=120).pack(side="right", padx=5)
        self.currency_combo = ctk.CTkComboBox(
            currency_frame,
            values=["ريال", "دولار", "يورو", "دينار"],
            width=300
        )
        self.currency_combo.pack(side="left", padx=5)
        self.currency_combo.set(app_settings.get('currency', 'ريال'))
        
        # إعدادات المخزون
        inventory_section = ctk.CTkFrame(content_frame)
        inventory_section.pack(fill="x", pady=10)
        
        ctk.CTkLabel(
            inventory_section,
            text="إعدادات المخزون",
            font=("Arial", 16, "bold")
        ).pack(pady=10)
        
        # تنبيه المخزون المنخفض
        low_stock_frame = ctk.CTkFrame(inventory_section)
        low_stock_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkLabel(low_stock_frame, text="تنبيه المخزون المنخفض:", width=120).pack(side="right", padx=5)
        self.low_stock_entry = ctk.CTkEntry(low_stock_frame, width=100)
        self.low_stock_entry.pack(side="left", padx=5)
        self.low_stock_entry.insert(0, str(app_settings.get('low_stock_alert', 10)))
        ctk.CTkLabel(low_stock_frame, text="وحدة").pack(side="left", padx=5)
        
        # إعدادات الواجهة
        ui_section = ctk.CTkFrame(content_frame)
        ui_section.pack(fill="x", pady=10)
        
        ctk.CTkLabel(
            ui_section,
            text="إعدادات الواجهة",
            font=("Arial", 16, "bold")
        ).pack(pady=10)
        
        # المظهر
        theme_frame = ctk.CTkFrame(ui_section)
        theme_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkLabel(theme_frame, text="المظهر:", width=120).pack(side="right", padx=5)
        self.theme_combo = ctk.CTkComboBox(
            theme_frame,
            values=["dark", "light"],
            width=300
        )
        self.theme_combo.pack(side="left", padx=5)
        self.theme_combo.set(app_settings.get('theme', 'dark'))
        
        # حجم الخط
        font_size_frame = ctk.CTkFrame(ui_section)
        font_size_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkLabel(font_size_frame, text="حجم الخط:", width=120).pack(side="right", padx=5)
        self.font_size_entry = ctk.CTkEntry(font_size_frame, width=100)
        self.font_size_entry.pack(side="left", padx=5)
        self.font_size_entry.insert(0, str(app_settings.get('font_size', 12)))
        
        # أزرار الحفظ والإلغاء
        buttons_frame = ctk.CTkFrame(content_frame)
        buttons_frame.pack(fill="x", pady=20)
        
        save_btn = ctk.CTkButton(
            buttons_frame,
            text="💾 حفظ الإعدادات",
            command=self.save_settings,
            width=150
        )
        save_btn.pack(side="left", padx=10)
        
        reset_btn = ctk.CTkButton(
            buttons_frame,
            text="🔄 إعادة تعيين",
            command=self.reset_settings,
            width=150,
            fg_color="orange"
        )
        reset_btn.pack(side="right", padx=10)
    
    def create_backup_tab(self):
        """إنشاء تبويب النسخ الاحتياطية"""
        backup_frame = ctk.CTkFrame(self.notebook)
        self.notebook.add(backup_frame, text="النسخ الاحتياطية")
        
        # إطار المحتوى
        content_frame = ctk.CTkScrollableFrame(backup_frame)
        content_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # عنوان
        title = ctk.CTkLabel(
            content_frame,
            text="إدارة النسخ الاحتياطية",
            font=("Arial", 16, "bold")
        )
        title.pack(pady=10)
        
        # معلومات قاعدة البيانات
        info_frame = ctk.CTkFrame(content_frame)
        info_frame.pack(fill="x", pady=10)
        
        ctk.CTkLabel(
            info_frame,
            text="معلومات قاعدة البيانات",
            font=("Arial", 14, "bold")
        ).pack(pady=10)
        
        # مسار قاعدة البيانات
        from config.database import db_manager
        db_path = os.path.abspath(db_manager.db_path)
        
        ctk.CTkLabel(info_frame, text=f"المسار: {db_path}").pack(anchor="w", padx=10, pady=2)
        
        # حجم قاعدة البيانات
        if os.path.exists(db_path):
            size_mb = os.path.getsize(db_path) / (1024 * 1024)
            ctk.CTkLabel(info_frame, text=f"الحجم: {size_mb:.2f} ميجابايت").pack(anchor="w", padx=10, pady=2)
        
        # آخر تعديل
        if os.path.exists(db_path):
            mtime = os.path.getmtime(db_path)
            last_modified = datetime.fromtimestamp(mtime).strftime("%Y-%m-%d %H:%M:%S")
            ctk.CTkLabel(info_frame, text=f"آخر تعديل: {last_modified}").pack(anchor="w", padx=10, pady=2)
        
        # أزرار النسخ الاحتياطية
        backup_buttons_frame = ctk.CTkFrame(content_frame)
        backup_buttons_frame.pack(fill="x", pady=20)
        
        # إنشاء نسخة احتياطية
        create_backup_btn = ctk.CTkButton(
            backup_buttons_frame,
            text="💾 إنشاء نسخة احتياطية",
            command=self.create_backup,
            width=200,
            height=40
        )
        create_backup_btn.pack(side="right", padx=10, pady=10)
        
        # استعادة نسخة احتياطية
        restore_backup_btn = ctk.CTkButton(
            backup_buttons_frame,
            text="📂 استعادة نسخة احتياطية",
            command=self.restore_backup,
            width=200,
            height=40,
            fg_color="orange"
        )
        restore_backup_btn.pack(side="left", padx=10, pady=10)
        
        # إعدادات النسخ الاحتياطي التلقائي
        auto_backup_frame = ctk.CTkFrame(content_frame)
        auto_backup_frame.pack(fill="x", pady=10)
        
        ctk.CTkLabel(
            auto_backup_frame,
            text="النسخ الاحتياطي التلقائي",
            font=("Arial", 14, "bold")
        ).pack(pady=10)
        
        # فترة النسخ الاحتياطي
        interval_frame = ctk.CTkFrame(auto_backup_frame)
        interval_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkLabel(interval_frame, text="فترة النسخ الاحتياطي:", width=150).pack(side="right", padx=5)
        self.backup_interval_entry = ctk.CTkEntry(interval_frame, width=100)
        self.backup_interval_entry.pack(side="left", padx=5)
        self.backup_interval_entry.insert(0, str(app_settings.get('backup_interval', 7)))
        ctk.CTkLabel(interval_frame, text="أيام").pack(side="left", padx=5)
        
        # تفعيل النسخ الاحتياطي التلقائي
        auto_backup_checkbox = ctk.CTkCheckBox(
            auto_backup_frame,
            text="تفعيل النسخ الاحتياطي التلقائي"
        )
        auto_backup_checkbox.pack(padx=10, pady=10)
    
    def create_info_tab(self):
        """إنشاء تبويب معلومات النظام"""
        info_frame = ctk.CTkFrame(self.notebook)
        self.notebook.add(info_frame, text="معلومات النظام")
        
        # إطار المحتوى
        content_frame = ctk.CTkScrollableFrame(info_frame)
        content_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # معلومات النظام
        system_info_frame = ctk.CTkFrame(content_frame)
        system_info_frame.pack(fill="x", pady=10)
        
        ctk.CTkLabel(
            system_info_frame,
            text="معلومات النظام",
            font=("Arial", 16, "bold")
        ).pack(pady=10)
        
        # اسم النظام
        ctk.CTkLabel(system_info_frame, text="اسم النظام: نظام إدارة المخازن").pack(anchor="w", padx=10, pady=2)
        ctk.CTkLabel(system_info_frame, text="الإصدار: 1.0.0").pack(anchor="w", padx=10, pady=2)
        ctk.CTkLabel(system_info_frame, text="تاريخ الإصدار: 2024-12-19").pack(anchor="w", padx=10, pady=2)
        ctk.CTkLabel(system_info_frame, text="المطور: Augment Agent").pack(anchor="w", padx=10, pady=2)
        
        # معلومات قاعدة البيانات
        db_info_frame = ctk.CTkFrame(content_frame)
        db_info_frame.pack(fill="x", pady=10)
        
        ctk.CTkLabel(
            db_info_frame,
            text="إحصائيات قاعدة البيانات",
            font=("Arial", 16, "bold")
        ).pack(pady=10)
        
        # حساب الإحصائيات
        from models.product import Product
        from models.supplier import Supplier
        from models.user import User
        from models.transaction import InventoryTransaction
        
        products_count = len(Product.get_all())
        suppliers_count = len(Supplier.get_all())
        users_count = len(User.get_all())
        transactions_count = len(InventoryTransaction.get_all())
        
        ctk.CTkLabel(db_info_frame, text=f"عدد المنتجات: {products_count}").pack(anchor="w", padx=10, pady=2)
        ctk.CTkLabel(db_info_frame, text=f"عدد الموردين: {suppliers_count}").pack(anchor="w", padx=10, pady=2)
        ctk.CTkLabel(db_info_frame, text=f"عدد المستخدمين: {users_count}").pack(anchor="w", padx=10, pady=2)
        ctk.CTkLabel(db_info_frame, text=f"عدد المعاملات: {transactions_count}").pack(anchor="w", padx=10, pady=2)
        
        # معلومات التقنية
        tech_info_frame = ctk.CTkFrame(content_frame)
        tech_info_frame.pack(fill="x", pady=10)
        
        ctk.CTkLabel(
            tech_info_frame,
            text="المعلومات التقنية",
            font=("Arial", 16, "bold")
        ).pack(pady=10)
        
        import sys
        import platform
        
        ctk.CTkLabel(tech_info_frame, text=f"Python: {sys.version.split()[0]}").pack(anchor="w", padx=10, pady=2)
        ctk.CTkLabel(tech_info_frame, text=f"نظام التشغيل: {platform.system()} {platform.release()}").pack(anchor="w", padx=10, pady=2)
        ctk.CTkLabel(tech_info_frame, text="واجهة المستخدم: CustomTkinter").pack(anchor="w", padx=10, pady=2)
        ctk.CTkLabel(tech_info_frame, text="قاعدة البيانات: SQLite").pack(anchor="w", padx=10, pady=2)
        
        # رخصة النظام
        license_frame = ctk.CTkFrame(content_frame)
        license_frame.pack(fill="x", pady=10)
        
        ctk.CTkLabel(
            license_frame,
            text="الرخصة",
            font=("Arial", 16, "bold")
        ).pack(pady=10)
        
        license_text = """
هذا النظام مرخص تحت رخصة MIT.
يمكن استخدامه وتعديله وتوزيعه بحرية.
تم تطويره بواسطة Augment Agent.
        """
        
        ctk.CTkLabel(license_frame, text=license_text.strip()).pack(anchor="w", padx=10, pady=5)
    
    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            # حفظ الإعدادات
            app_settings.set('company_name', self.company_name_entry.get().strip())
            app_settings.set('currency', self.currency_combo.get())
            app_settings.set('low_stock_alert', int(self.low_stock_entry.get() or 10))
            app_settings.set('theme', self.theme_combo.get())
            app_settings.set('font_size', int(self.font_size_entry.get() or 12))
            app_settings.set('backup_interval', int(self.backup_interval_entry.get() or 7))
            
            messagebox.showinfo("نجح", "تم حفظ الإعدادات بنجاح\nسيتم تطبيق بعض الإعدادات عند إعادة تشغيل البرنامج")
            
        except ValueError:
            messagebox.showerror("خطأ", "يرجى التأكد من صحة القيم المدخلة")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ الإعدادات:\n{str(e)}")
    
    def reset_settings(self):
        """إعادة تعيين الإعدادات"""
        result = messagebox.askyesno(
            "تأكيد إعادة التعيين",
            "هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟"
        )
        
        if result:
            # إعادة تعيين القيم في الواجهة
            self.company_name_entry.delete(0, 'end')
            self.company_name_entry.insert(0, "شركة إدارة المخازن")
            
            self.currency_combo.set("ريال")
            
            self.low_stock_entry.delete(0, 'end')
            self.low_stock_entry.insert(0, "10")
            
            self.theme_combo.set("dark")
            
            self.font_size_entry.delete(0, 'end')
            self.font_size_entry.insert(0, "12")
            
            self.backup_interval_entry.delete(0, 'end')
            self.backup_interval_entry.insert(0, "7")
            
            messagebox.showinfo("تم", "تم إعادة تعيين الإعدادات\nاضغط 'حفظ الإعدادات' لتطبيق التغييرات")
    
    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        try:
            # اختيار مكان الحفظ
            filename = filedialog.asksaveasfilename(
                defaultextension=".db",
                filetypes=[("Database files", "*.db"), ("All files", "*.*")],
                title="حفظ النسخة الاحتياطية",
                initialname=f"warehouse_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
            )
            
            if not filename:
                return
            
            # إنشاء النسخة الاحتياطية
            backup_path = backup_database(filename)
            
            if backup_path:
                messagebox.showinfo("نجح", f"تم إنشاء النسخة الاحتياطية بنجاح:\n{backup_path}")
            else:
                messagebox.showerror("خطأ", "فشل في إنشاء النسخة الاحتياطية")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إنشاء النسخة الاحتياطية:\n{str(e)}")
    
    def restore_backup(self):
        """استعادة نسخة احتياطية"""
        try:
            # تحذير المستخدم
            result = messagebox.askyesno(
                "تحذير",
                "استعادة النسخة الاحتياطية ستحل محل البيانات الحالية.\nهل أنت متأكد من المتابعة؟"
            )
            
            if not result:
                return
            
            # اختيار ملف النسخة الاحتياطية
            filename = filedialog.askopenfilename(
                filetypes=[("Database files", "*.db"), ("All files", "*.*")],
                title="اختيار النسخة الاحتياطية"
            )
            
            if not filename:
                return
            
            # استعادة النسخة الاحتياطية
            if restore_database(filename):
                messagebox.showinfo(
                    "نجح", 
                    "تم استعادة النسخة الاحتياطية بنجاح\nيرجى إعادة تشغيل البرنامج لتطبيق التغييرات"
                )
            else:
                messagebox.showerror("خطأ", "فشل في استعادة النسخة الاحتياطية")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء استعادة النسخة الاحتياطية:\n{str(e)}")
