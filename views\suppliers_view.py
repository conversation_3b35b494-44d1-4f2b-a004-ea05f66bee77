import customtkinter as ctk
import tkinter as tk
from tkinter import ttk, messagebox

from models.supplier import Supplier
from utils.validators import SupplierValidator

class SuppliersView:
    def __init__(self, parent, main_window):
        self.parent = parent
        self.main_window = main_window
        self.current_supplier = None
        self.suppliers_data = []
        self.setup_ui()
        self.load_suppliers()
    
    def setup_ui(self):
        """إعداد واجهة إدارة الموردين"""
        # إطار رئيسي
        self.main_frame = ctk.CTkFrame(self.parent)
        self.main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # عنوان
        title = ctk.CTkLabel(
            self.main_frame,
            text="إدارة الموردين",
            font=("Arial", 20, "bold")
        )
        title.pack(pady=10)
        
        # إطار الأزرار العلوية
        buttons_frame = ctk.CTkFrame(self.main_frame)
        buttons_frame.pack(fill="x", padx=10, pady=5)
        
        # أزرار التحكم
        add_btn = ctk.CTkButton(
            buttons_frame,
            text="➕ إضافة مورد",
            command=self.add_supplier,
            width=120
        )
        add_btn.pack(side="right", padx=5, pady=10)
        
        edit_btn = ctk.CTkButton(
            buttons_frame,
            text="✏️ تعديل",
            command=self.edit_supplier,
            width=120
        )
        edit_btn.pack(side="right", padx=5, pady=10)
        
        delete_btn = ctk.CTkButton(
            buttons_frame,
            text="🗑️ حذف",
            command=self.delete_supplier,
            width=120,
            fg_color="red"
        )
        delete_btn.pack(side="right", padx=5, pady=10)
        
        # إطار البحث
        search_frame = ctk.CTkFrame(buttons_frame)
        search_frame.pack(side="left", padx=10, pady=10)
        
        search_label = ctk.CTkLabel(search_frame, text="البحث:")
        search_label.pack(side="right", padx=5)
        
        self.search_entry = ctk.CTkEntry(
            search_frame,
            placeholder_text="ابحث عن مورد...",
            width=200
        )
        self.search_entry.pack(side="right", padx=5)
        self.search_entry.bind("<KeyRelease>", self.search_suppliers)
        
        # إطار المحتوى الرئيسي
        content_frame = ctk.CTkFrame(self.main_frame)
        content_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        # جدول الموردين
        self.create_suppliers_table(content_frame)
    
    def create_suppliers_table(self, parent):
        """إنشاء جدول الموردين"""
        # إطار الجدول
        table_frame = ctk.CTkFrame(parent)
        table_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # أعمدة الجدول
        columns = ("الاسم", "الشخص المسؤول", "الهاتف", "البريد الإلكتروني", "العنوان", "الرقم الضريبي", "الحالة")
        
        self.suppliers_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)
        
        # تعيين عناوين الأعمدة وعرضها
        column_widths = [150, 120, 120, 180, 200, 120, 80]
        for i, col in enumerate(columns):
            self.suppliers_tree.heading(col, text=col)
            self.suppliers_tree.column(col, width=column_widths[i], anchor="center")
        
        # شريط التمرير
        scrollbar_y = ttk.Scrollbar(table_frame, orient="vertical", command=self.suppliers_tree.yview)
        scrollbar_x = ttk.Scrollbar(table_frame, orient="horizontal", command=self.suppliers_tree.xview)
        
        self.suppliers_tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
        
        # تخطيط الجدول
        self.suppliers_tree.pack(side="right", fill="both", expand=True)
        scrollbar_y.pack(side="left", fill="y")
        scrollbar_x.pack(side="bottom", fill="x")
        
        # ربط النقر المزدوج بالتعديل
        self.suppliers_tree.bind("<Double-1>", lambda e: self.edit_supplier())
    
    def load_suppliers(self):
        """تحميل الموردين في الجدول"""
        # مسح البيانات الحالية
        for item in self.suppliers_tree.get_children():
            self.suppliers_tree.delete(item)
        
        # تحميل الموردين
        self.suppliers_data = Supplier.get_all()
        
        for supplier in self.suppliers_data:
            status = "نشط" if supplier.is_active else "غير نشط"
            
            self.suppliers_tree.insert("", "end", values=(
                supplier.name,
                supplier.contact_person or "",
                supplier.phone or "",
                supplier.email or "",
                supplier.address or "",
                supplier.tax_number or "",
                status
            ))
    
    def search_suppliers(self, event=None):
        """البحث في الموردين"""
        query = self.search_entry.get().strip()
        
        if not query:
            self.load_suppliers()
            return
        
        # مسح البيانات الحالية
        for item in self.suppliers_tree.get_children():
            self.suppliers_tree.delete(item)
        
        # البحث في الموردين
        search_results = Supplier.search(query)
        
        for supplier in search_results:
            status = "نشط" if supplier.is_active else "غير نشط"
            
            self.suppliers_tree.insert("", "end", values=(
                supplier.name,
                supplier.contact_person or "",
                supplier.phone or "",
                supplier.email or "",
                supplier.address or "",
                supplier.tax_number or "",
                status
            ))
    
    def get_selected_supplier(self):
        """الحصول على المورد المحدد"""
        selection = self.suppliers_tree.selection()
        if not selection:
            return None
        
        item = self.suppliers_tree.item(selection[0])
        supplier_name = item['values'][0]
        
        # البحث عن المورد في البيانات المحملة
        for supplier in self.suppliers_data:
            if supplier.name == supplier_name:
                return supplier
        
        return None
    
    def add_supplier(self):
        """إضافة مورد جديد"""
        self.current_supplier = None
        self.show_supplier_form()
    
    def edit_supplier(self):
        """تعديل مورد موجود"""
        selected_supplier = self.get_selected_supplier()
        if not selected_supplier:
            messagebox.showwarning("تحذير", "يرجى اختيار مورد للتعديل")
            return
        
        self.current_supplier = selected_supplier
        self.show_supplier_form()
    
    def delete_supplier(self):
        """حذف مورد"""
        selected_supplier = self.get_selected_supplier()
        if not selected_supplier:
            messagebox.showwarning("تحذير", "يرجى اختيار مورد للحذف")
            return
        
        result = messagebox.askyesno(
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف المورد '{selected_supplier.name}'؟"
        )
        
        if result:
            selected_supplier.delete()
            self.load_suppliers()
            messagebox.showinfo("نجح", "تم حذف المورد بنجاح")
    
    def show_supplier_form(self):
        """عرض نموذج إضافة/تعديل المورد"""
        # إنشاء نافذة جديدة
        form_window = ctk.CTkToplevel(self.parent)
        form_window.title("إضافة مورد" if not self.current_supplier else "تعديل مورد")
        form_window.geometry("500x600")
        form_window.transient(self.parent)
        form_window.grab_set()
        
        # إطار رئيسي مع شريط تمرير
        main_frame = ctk.CTkScrollableFrame(form_window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # عنوان النموذج
        title = ctk.CTkLabel(
            main_frame,
            text="إضافة مورد جديد" if not self.current_supplier else "تعديل المورد",
            font=("Arial", 18, "bold")
        )
        title.pack(pady=10)
        
        # حقول النموذج
        fields = {}
        
        # اسم المورد
        name_frame = ctk.CTkFrame(main_frame)
        name_frame.pack(fill="x", pady=5)
        
        ctk.CTkLabel(name_frame, text="اسم المورد *", width=120).pack(side="right", padx=5)
        fields['name'] = ctk.CTkEntry(name_frame, width=300)
        fields['name'].pack(side="left", padx=5)
        
        # الشخص المسؤول
        contact_frame = ctk.CTkFrame(main_frame)
        contact_frame.pack(fill="x", pady=5)
        
        ctk.CTkLabel(contact_frame, text="الشخص المسؤول", width=120).pack(side="right", padx=5)
        fields['contact_person'] = ctk.CTkEntry(contact_frame, width=300)
        fields['contact_person'].pack(side="left", padx=5)
        
        # رقم الهاتف
        phone_frame = ctk.CTkFrame(main_frame)
        phone_frame.pack(fill="x", pady=5)
        
        ctk.CTkLabel(phone_frame, text="رقم الهاتف", width=120).pack(side="right", padx=5)
        fields['phone'] = ctk.CTkEntry(phone_frame, width=300)
        fields['phone'].pack(side="left", padx=5)
        
        # البريد الإلكتروني
        email_frame = ctk.CTkFrame(main_frame)
        email_frame.pack(fill="x", pady=5)
        
        ctk.CTkLabel(email_frame, text="البريد الإلكتروني", width=120).pack(side="right", padx=5)
        fields['email'] = ctk.CTkEntry(email_frame, width=300)
        fields['email'].pack(side="left", padx=5)
        
        # العنوان
        address_frame = ctk.CTkFrame(main_frame)
        address_frame.pack(fill="x", pady=5)
        
        ctk.CTkLabel(address_frame, text="العنوان", width=120).pack(side="right", padx=5)
        fields['address'] = ctk.CTkTextbox(address_frame, width=300, height=80)
        fields['address'].pack(side="left", padx=5)
        
        # الرقم الضريبي
        tax_frame = ctk.CTkFrame(main_frame)
        tax_frame.pack(fill="x", pady=5)
        
        ctk.CTkLabel(tax_frame, text="الرقم الضريبي", width=120).pack(side="right", padx=5)
        fields['tax_number'] = ctk.CTkEntry(tax_frame, width=300)
        fields['tax_number'].pack(side="left", padx=5)
        
        # الحالة
        status_frame = ctk.CTkFrame(main_frame)
        status_frame.pack(fill="x", pady=5)
        
        ctk.CTkLabel(status_frame, text="الحالة", width=120).pack(side="right", padx=5)
        fields['is_active'] = ctk.CTkCheckBox(status_frame, text="نشط")
        fields['is_active'].pack(side="left", padx=5)
        
        # ملء البيانات في حالة التعديل
        if self.current_supplier:
            self.fill_supplier_form(fields)
        else:
            # قيم افتراضية للمورد الجديد
            fields['is_active'].select()
        
        # أزرار الحفظ والإلغاء
        buttons_frame = ctk.CTkFrame(main_frame)
        buttons_frame.pack(fill="x", pady=20)
        
        save_btn = ctk.CTkButton(
            buttons_frame,
            text="💾 حفظ",
            command=lambda: self.save_supplier(fields, form_window),
            width=120
        )
        save_btn.pack(side="left", padx=10)
        
        cancel_btn = ctk.CTkButton(
            buttons_frame,
            text="❌ إلغاء",
            command=form_window.destroy,
            width=120,
            fg_color="gray"
        )
        cancel_btn.pack(side="right", padx=10)
    
    def fill_supplier_form(self, fields):
        """ملء نموذج المورد بالبيانات الحالية"""
        if not self.current_supplier:
            return
        
        fields['name'].insert(0, self.current_supplier.name or "")
        fields['contact_person'].insert(0, self.current_supplier.contact_person or "")
        fields['phone'].insert(0, self.current_supplier.phone or "")
        fields['email'].insert(0, self.current_supplier.email or "")
        fields['address'].insert("1.0", self.current_supplier.address or "")
        fields['tax_number'].insert(0, self.current_supplier.tax_number or "")
        
        if self.current_supplier.is_active:
            fields['is_active'].select()
    
    def save_supplier(self, fields, form_window):
        """حفظ المورد"""
        try:
            # جمع البيانات من النموذج
            data = {
                'name': fields['name'].get().strip(),
                'contact_person': fields['contact_person'].get().strip(),
                'phone': fields['phone'].get().strip(),
                'email': fields['email'].get().strip(),
                'address': fields['address'].get("1.0", "end-1c").strip(),
                'tax_number': fields['tax_number'].get().strip(),
                'is_active': fields['is_active'].get()
            }
            
            # التحقق من صحة البيانات
            errors = SupplierValidator.validate_supplier_data(data)
            if errors:
                messagebox.showerror("خطأ في البيانات", "\n".join(errors))
                return
            
            # إنشاء أو تحديث المورد
            if self.current_supplier:
                # تحديث مورد موجود
                self.current_supplier.name = data['name']
                self.current_supplier.contact_person = data['contact_person']
                self.current_supplier.phone = data['phone']
                self.current_supplier.email = data['email']
                self.current_supplier.address = data['address']
                self.current_supplier.tax_number = data['tax_number']
                self.current_supplier.is_active = data['is_active']
                
                self.current_supplier.save()
                messagebox.showinfo("نجح", "تم تحديث المورد بنجاح")
            else:
                # إضافة مورد جديد
                new_supplier = Supplier(
                    name=data['name'],
                    contact_person=data['contact_person'],
                    phone=data['phone'],
                    email=data['email'],
                    address=data['address'],
                    tax_number=data['tax_number'],
                    is_active=data['is_active']
                )
                
                new_supplier.save()
                messagebox.showinfo("نجح", "تم إضافة المورد بنجاح")
            
            # إغلاق النموذج وتحديث الجدول
            form_window.destroy()
            self.load_suppliers()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ المورد:\n{str(e)}")
