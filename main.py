#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة المخازن
Warehouse Management System

نظام شامل لإدارة المخازن والمنتجات والموردين
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    # استيراد المكتبات المطلوبة
    import customtkinter as ctk
    from PIL import Image, ImageTk
    
    # استيراد وحدات النظام
    from config.database import db_manager
    from config.settings import app_settings
    from views.main_window import MainWindow
    
except ImportError as e:
    # في حالة عدم وجود المكتبات المطلوبة
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية
    
    error_msg = f"""
خطأ في استيراد المكتبات المطلوبة:
{str(e)}

يرجى تثبيت المكتبات المطلوبة باستخدام الأمر التالي:
pip install -r requirements.txt

أو تثبيت المكتبات يدوياً:
pip install customtkinter pillow reportlab matplotlib python-barcode qrcode openpyxl tkcalendar
"""
    
    messagebox.showerror("خطأ في المكتبات", error_msg)
    sys.exit(1)

def check_requirements():
    """التحقق من المتطلبات الأساسية"""
    try:
        # التحقق من وجود مجلد البيانات
        if not os.path.exists('data'):
            os.makedirs('data')
        
        # التحقق من وجود مجلد الأصول
        if not os.path.exists('assets'):
            os.makedirs('assets')
            os.makedirs('assets/icons')
            os.makedirs('assets/images')
        
        # اختبار الاتصال بقاعدة البيانات
        conn = db_manager.get_connection()
        conn.close()
        
        return True
        
    except Exception as e:
        messagebox.showerror("خطأ في التهيئة", f"حدث خطأ أثناء تهيئة النظام:\n{str(e)}")
        return False

def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    try:
        # التحقق من المتطلبات
        if not check_requirements():
            return
        
        # إنشاء وتشغيل النافذة الرئيسية
        app = MainWindow()
        app.run()
        
    except Exception as e:
        # التعامل مع الأخطاء غير المتوقعة
        error_msg = f"حدث خطأ غير متوقع:\n{str(e)}\n\nيرجى إعادة تشغيل التطبيق."
        
        # محاولة عرض رسالة الخطأ
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("خطأ", error_msg)
        except:
            print(error_msg)
        
        sys.exit(1)

if __name__ == "__main__":
    main()
