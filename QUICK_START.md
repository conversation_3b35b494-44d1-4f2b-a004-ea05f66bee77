# دليل البدء السريع - نظام إدارة المخازن 🚀

## التثبيت والتشغيل السريع

### 1. متطلبات النظام
- Python 3.8 أو أحدث
- نظام التشغيل: Windows, macOS, Linux

### 2. التثبيت السريع (Windows)
```bash
# تشغيل ملف الإعداد التلقائي
setup.bat
```

### 3. التثبيت اليدوي
```bash
# تثبيت المكتبة الأساسية
pip install customtkinter

# تشغيل البرنامج
python main.py
```

### 4. إنشاء البيانات التجريبية
```bash
# إعادة تعيين قاعدة البيانات وإنشاء بيانات تجريبية
python reset_database.py
```

## بيانات تسجيل الدخول 🔐

### المدير العام
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123
- **الصلاحيات:** جميع الصلاحيات

### مشرف المخزن
- **اسم المستخدم:** manager1
- **كلمة المرور:** manager123
- **الصلاحيات:** إدارة المنتجات والموردين والتقارير

### موظف المخزن
- **اسم المستخدم:** employee1
- **كلمة المرور:** emp123
- **الصلاحيات:** عرض المخزون وإضافة المعاملات

## الوحدات الرئيسية 📋

### 🏠 لوحة المعلومات
- عرض إحصائيات سريعة
- تنبيهات المخزون المنخفض
- آخر المعاملات

### 📦 إدارة المنتجات
- إضافة/تعديل/حذف المنتجات
- تصنيف المنتجات
- إدارة الباركود
- تحديد الحد الأدنى والأقصى للمخزون

### 📊 إدارة المخزون
- عرض حالة المخزون الحالية
- تنبيهات نقص المخزون
- عمليات الجرد

### 🚚 إدارة الواردات
- تسجيل أوامر التوريد
- استقبال البضائع
- فواتير الشراء

### 📤 إدارة الصادرات
- صرف المنتجات
- أوامر البيع
- تتبع المبيعات

### 🔄 حركات المخزون
- سجل جميع الحركات
- البحث والتصفية
- تصدير التقارير

### 🏢 إدارة الموردين
- بيانات الموردين
- سجل التعاملات
- تقييم الموردين

### 📈 التقارير والإحصائيات
- تقارير المخزون
- تقارير المبيعات
- تقارير الموردين
- الرسوم البيانية

### 👥 إدارة المستخدمين (للمدير فقط)
- إضافة/تعديل المستخدمين
- تحديد الصلاحيات
- تتبع نشاط المستخدمين

### ⚙️ الإعدادات
- إعدادات النظام
- إعدادات الطباعة
- النسخ الاحتياطية

## البيانات التجريبية المتوفرة 📋

### التصنيفات
- أثاث مكتبي
- أجهزة كمبيوتر
- قرطاسية
- معدات شبكات
- أثاث منزلي
- إلكترونيات

### المنتجات (8 منتجات)
- مكتب مدير خشبي
- كرسي مكتب دوار
- لابتوب ديل
- ماوس لاسلكي
- أقلام جاف زرقاء
- ورق A4 أبيض
- راوتر واي فاي
- كابل شبكة

### الموردين (3 موردين)
- شركة التقنية المتقدمة
- مؤسسة الأثاث الحديث
- شركة القرطاسية الذكية

## نصائح للاستخدام 💡

### إدارة المنتجات
1. استخدم أكواد واضحة للمنتجات (مثل: DESK001, CHAIR001)
2. حدد الحد الأدنى للمخزون لتلقي التنبيهات
3. أضف صور للمنتجات لسهولة التعرف عليها
4. استخدم التصنيفات لتنظيم المنتجات

### إدارة المخزون
1. راقب التنبيهات بانتظام
2. قم بعمليات جرد دورية
3. تحقق من دقة الكميات المدخلة
4. استخدم المعاملات لتتبع الحركات

### التقارير
1. اختر الفترة الزمنية المناسبة
2. صدر التقارير إلى Excel للتحليل
3. استخدم الرسوم البيانية لفهم الاتجاهات
4. راجع التقارير بانتظام لاتخاذ القرارات

## استكشاف الأخطاء 🔧

### مشاكل شائعة وحلولها

#### البرنامج لا يبدأ
```bash
# تأكد من تثبيت Python
python --version

# تثبيت المكتبات المطلوبة
pip install customtkinter
```

#### خطأ في قاعدة البيانات
```bash
# إعادة إنشاء قاعدة البيانات
python reset_database.py
```

#### مشاكل في الواجهة
- تأكد من دقة الشاشة (1024x768 أو أعلى)
- جرب تغيير حجم النافذة
- أعد تشغيل البرنامج

#### بطء في الأداء
- أغلق البرامج الأخرى
- تأكد من وجود مساحة كافية على القرص الصلب
- أعد تشغيل الكمبيوتر

## الدعم والمساعدة 🆘

### ملفات مهمة
- `README.md` - دليل شامل
- `main.py` - الملف الرئيسي
- `requirements.txt` - المتطلبات
- `data/warehouse.db` - قاعدة البيانات

### أوامر مفيدة
```bash
# تشغيل البرنامج
python main.py

# إنشاء بيانات تجريبية
python create_sample_data.py

# إعادة تعيين النظام
python reset_database.py

# تثبيت المتطلبات
pip install -r requirements.txt
```

---

**نظام إدارة المخازن - تم تطويره بواسطة Augment Agent** 🤖
