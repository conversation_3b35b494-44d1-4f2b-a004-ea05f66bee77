import os
import shutil
from datetime import datetime, date
import barcode
from barcode.writer import ImageWriter
import qrcode
from PIL import Image, ImageTk
import tkinter as tk

def generate_barcode(code, save_path=None):
    """إنشاء باركود للمنتج"""
    try:
        # إنشاء باركود Code128
        code128 = barcode.get_barcode_class('code128')
        barcode_instance = code128(code, writer=ImageWriter())
        
        if save_path:
            # حفظ الباركود كصورة
            filename = barcode_instance.save(save_path)
            return filename
        else:
            # إرجاع الباركود كصورة في الذاكرة
            from io import BytesIO
            buffer = BytesIO()
            barcode_instance.write(buffer)
            buffer.seek(0)
            return buffer
    except Exception as e:
        print(f"خطأ في إنشاء الباركود: {e}")
        return None

def generate_qr_code(data, save_path=None):
    """إنشاء QR Code"""
    try:
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(data)
        qr.make(fit=True)
        
        img = qr.make_image(fill_color="black", back_color="white")
        
        if save_path:
            img.save(save_path)
            return save_path
        else:
            return img
    except Exception as e:
        print(f"خطأ في إنشاء QR Code: {e}")
        return None

def format_currency(amount, currency="ريال"):
    """تنسيق العملة"""
    try:
        return f"{amount:,.2f} {currency}"
    except:
        return f"{amount} {currency}"

def format_date(date_obj, format_str="%Y-%m-%d"):
    """تنسيق التاريخ"""
    if isinstance(date_obj, str):
        try:
            date_obj = datetime.strptime(date_obj, "%Y-%m-%d %H:%M:%S")
        except:
            return date_obj
    
    if isinstance(date_obj, (datetime, date)):
        return date_obj.strftime(format_str)
    
    return str(date_obj)

def format_datetime(datetime_obj, format_str="%Y-%m-%d %H:%M"):
    """تنسيق التاريخ والوقت"""
    return format_date(datetime_obj, format_str)

def validate_number(value, allow_negative=False, allow_decimal=True):
    """التحقق من صحة الرقم"""
    try:
        if allow_decimal:
            num = float(value)
        else:
            num = int(value)
        
        if not allow_negative and num < 0:
            return False
        
        return True
    except:
        return False

def validate_email(email):
    """التحقق من صحة البريد الإلكتروني"""
    import re
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_phone(phone):
    """التحقق من صحة رقم الهاتف"""
    import re
    # نمط للأرقام السعودية
    pattern = r'^(05|5)[0-9]{8}$'
    # إزالة المسافات والرموز
    clean_phone = re.sub(r'[^\d]', '', phone)
    return re.match(pattern, clean_phone) is not None

def resize_image(image_path, max_width=200, max_height=200):
    """تغيير حجم الصورة"""
    try:
        with Image.open(image_path) as img:
            # حساب النسبة للحفاظ على التناسب
            ratio = min(max_width/img.width, max_height/img.height)
            new_width = int(img.width * ratio)
            new_height = int(img.height * ratio)
            
            # تغيير الحجم
            resized_img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
            return resized_img
    except Exception as e:
        print(f"خطأ في تغيير حجم الصورة: {e}")
        return None

def load_image_for_tkinter(image_path, max_width=200, max_height=200):
    """تحميل صورة لاستخدامها في Tkinter"""
    try:
        resized_img = resize_image(image_path, max_width, max_height)
        if resized_img:
            return ImageTk.PhotoImage(resized_img)
    except Exception as e:
        print(f"خطأ في تحميل الصورة: {e}")
    return None

def backup_database(backup_path=None):
    """إنشاء نسخة احتياطية من قاعدة البيانات"""
    try:
        from config.database import db_manager
        
        if not backup_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"backup/warehouse_backup_{timestamp}.db"
        
        # إنشاء مجلد النسخ الاحتياطية
        os.makedirs(os.path.dirname(backup_path), exist_ok=True)
        
        # نسخ قاعدة البيانات
        shutil.copy2(db_manager.db_path, backup_path)
        
        return backup_path
    except Exception as e:
        print(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
        return None

def restore_database(backup_path):
    """استعادة قاعدة البيانات من نسخة احتياطية"""
    try:
        from config.database import db_manager
        
        if not os.path.exists(backup_path):
            return False
        
        # إنشاء نسخة احتياطية من قاعدة البيانات الحالية
        current_backup = backup_database()
        
        # استعادة قاعدة البيانات
        shutil.copy2(backup_path, db_manager.db_path)
        
        return True
    except Exception as e:
        print(f"خطأ في استعادة قاعدة البيانات: {e}")
        return False

def export_to_excel(data, filename, sheet_name="البيانات"):
    """تصدير البيانات إلى Excel"""
    try:
        import openpyxl
        from openpyxl.styles import Font, Alignment
        
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = sheet_name
        
        # إضافة البيانات
        for row_idx, row_data in enumerate(data, 1):
            for col_idx, cell_data in enumerate(row_data, 1):
                cell = ws.cell(row=row_idx, column=col_idx, value=cell_data)
                
                # تنسيق الصف الأول (العناوين)
                if row_idx == 1:
                    cell.font = Font(bold=True)
                    cell.alignment = Alignment(horizontal='center')
        
        # حفظ الملف
        wb.save(filename)
        return True
    except Exception as e:
        print(f"خطأ في تصدير البيانات: {e}")
        return False

def generate_report_number(prefix="RPT"):
    """إنشاء رقم تقرير فريد"""
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    return f"{prefix}-{timestamp}"

def generate_order_number(prefix="ORD"):
    """إنشاء رقم أمر فريد"""
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    return f"{prefix}-{timestamp}"

def calculate_age_in_days(date_obj):
    """حساب العمر بالأيام"""
    if isinstance(date_obj, str):
        try:
            date_obj = datetime.strptime(date_obj, "%Y-%m-%d")
        except:
            return 0
    
    if isinstance(date_obj, datetime):
        date_obj = date_obj.date()
    
    if isinstance(date_obj, date):
        return (date.today() - date_obj).days
    
    return 0

def get_file_size_mb(file_path):
    """الحصول على حجم الملف بالميجابايت"""
    try:
        size_bytes = os.path.getsize(file_path)
        size_mb = size_bytes / (1024 * 1024)
        return round(size_mb, 2)
    except:
        return 0

def clean_filename(filename):
    """تنظيف اسم الملف من الأحرف غير المسموحة"""
    import re
    # إزالة الأحرف غير المسموحة
    clean_name = re.sub(r'[<>:"/\\|?*]', '_', filename)
    return clean_name

def center_window(window, width=None, height=None):
    """توسيط النافذة على الشاشة"""
    window.update_idletasks()
    
    if width is None:
        width = window.winfo_width()
    if height is None:
        height = window.winfo_height()
    
    screen_width = window.winfo_screenwidth()
    screen_height = window.winfo_screenheight()
    
    x = (screen_width - width) // 2
    y = (screen_height - height) // 2
    
    window.geometry(f"{width}x{height}+{x}+{y}")

def show_loading_dialog(parent, message="جاري التحميل..."):
    """عرض نافذة تحميل"""
    import tkinter as tk
    from tkinter import ttk
    
    loading_window = tk.Toplevel(parent)
    loading_window.title("تحميل")
    loading_window.geometry("300x100")
    loading_window.resizable(False, False)
    loading_window.transient(parent)
    loading_window.grab_set()
    
    # توسيط النافذة
    center_window(loading_window, 300, 100)
    
    # رسالة التحميل
    label = tk.Label(loading_window, text=message, font=("Arial", 12))
    label.pack(pady=20)
    
    # شريط التقدم
    progress = ttk.Progressbar(loading_window, mode='indeterminate')
    progress.pack(pady=10, padx=20, fill='x')
    progress.start()
    
    return loading_window
