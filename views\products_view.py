import customtkinter as ctk
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from PIL import Image, ImageTk
import os

from models.product import Product, Category
from utils.validators import ProductValidator
from utils.helpers import generate_barcode, load_image_for_tkinter

class ProductsView:
    def __init__(self, parent, main_window):
        self.parent = parent
        self.main_window = main_window
        self.current_product = None
        self.products_data = []
        self.setup_ui()
        self.load_products()
    
    def setup_ui(self):
        """إعداد واجهة إدارة المنتجات"""
        # إطار رئيسي
        self.main_frame = ctk.CTkFrame(self.parent)
        self.main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # عنوان
        title = ctk.CTkLabel(
            self.main_frame,
            text="إدارة المنتجات",
            font=("Arial", 20, "bold")
        )
        title.pack(pady=10)
        
        # إطار الأزرار العلوية
        buttons_frame = ctk.CTkFrame(self.main_frame)
        buttons_frame.pack(fill="x", padx=10, pady=5)
        
        # أزرار التحكم
        add_btn = ctk.CTkButton(
            buttons_frame,
            text="➕ إضافة منتج",
            command=self.add_product,
            width=120
        )
        add_btn.pack(side="right", padx=5, pady=10)
        
        edit_btn = ctk.CTkButton(
            buttons_frame,
            text="✏️ تعديل",
            command=self.edit_product,
            width=120
        )
        edit_btn.pack(side="right", padx=5, pady=10)
        
        delete_btn = ctk.CTkButton(
            buttons_frame,
            text="🗑️ حذف",
            command=self.delete_product,
            width=120,
            fg_color="red"
        )
        delete_btn.pack(side="right", padx=5, pady=10)
        
        # إطار البحث
        search_frame = ctk.CTkFrame(buttons_frame)
        search_frame.pack(side="left", padx=10, pady=10)
        
        search_label = ctk.CTkLabel(search_frame, text="البحث:")
        search_label.pack(side="right", padx=5)
        
        self.search_entry = ctk.CTkEntry(
            search_frame,
            placeholder_text="ابحث عن منتج...",
            width=200
        )
        self.search_entry.pack(side="right", padx=5)
        self.search_entry.bind("<KeyRelease>", self.search_products)
        
        # إطار المحتوى الرئيسي
        content_frame = ctk.CTkFrame(self.main_frame)
        content_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        # جدول المنتجات
        self.create_products_table(content_frame)
    
    def create_products_table(self, parent):
        """إنشاء جدول المنتجات"""
        # إطار الجدول
        table_frame = ctk.CTkFrame(parent)
        table_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # أعمدة الجدول
        columns = ("الكود", "الاسم", "التصنيف", "الوحدة", "الكمية الحالية", "الحد الأدنى", "سعر التكلفة", "سعر البيع", "الحالة")
        
        self.products_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.products_tree.heading(col, text=col)
            self.products_tree.column(col, width=120, anchor="center")
        
        # شريط التمرير
        scrollbar_y = ttk.Scrollbar(table_frame, orient="vertical", command=self.products_tree.yview)
        scrollbar_x = ttk.Scrollbar(table_frame, orient="horizontal", command=self.products_tree.xview)
        
        self.products_tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
        
        # تخطيط الجدول
        self.products_tree.pack(side="right", fill="both", expand=True)
        scrollbar_y.pack(side="left", fill="y")
        scrollbar_x.pack(side="bottom", fill="x")
        
        # ربط النقر المزدوج بالتعديل
        self.products_tree.bind("<Double-1>", lambda e: self.edit_product())
    
    def load_products(self):
        """تحميل المنتجات في الجدول"""
        # مسح البيانات الحالية
        for item in self.products_tree.get_children():
            self.products_tree.delete(item)
        
        # تحميل المنتجات
        self.products_data = Product.get_all()
        
        for product in self.products_data:
            status = "نشط" if product.is_active else "غير نشط"
            category_name = getattr(product, 'category_name', '') or ''
            
            self.products_tree.insert("", "end", values=(
                product.code,
                product.name,
                category_name,
                product.unit,
                product.current_quantity,
                product.min_quantity,
                f"{product.cost_price:.2f}",
                f"{product.selling_price:.2f}",
                status
            ))
    
    def search_products(self, event=None):
        """البحث في المنتجات"""
        query = self.search_entry.get().strip()
        
        if not query:
            self.load_products()
            return
        
        # مسح البيانات الحالية
        for item in self.products_tree.get_children():
            self.products_tree.delete(item)
        
        # البحث في المنتجات
        search_results = Product.search(query)
        
        for product in search_results:
            status = "نشط" if product.is_active else "غير نشط"
            category_name = getattr(product, 'category_name', '') or ''
            
            self.products_tree.insert("", "end", values=(
                product.code,
                product.name,
                category_name,
                product.unit,
                product.current_quantity,
                product.min_quantity,
                f"{product.cost_price:.2f}",
                f"{product.selling_price:.2f}",
                status
            ))
    
    def get_selected_product(self):
        """الحصول على المنتج المحدد"""
        selection = self.products_tree.selection()
        if not selection:
            return None
        
        item = self.products_tree.item(selection[0])
        product_code = item['values'][0]
        
        # البحث عن المنتج في البيانات المحملة
        for product in self.products_data:
            if product.code == product_code:
                return product
        
        return None
    
    def add_product(self):
        """إضافة منتج جديد"""
        self.current_product = None
        self.show_product_form()
    
    def edit_product(self):
        """تعديل منتج موجود"""
        selected_product = self.get_selected_product()
        if not selected_product:
            messagebox.showwarning("تحذير", "يرجى اختيار منتج للتعديل")
            return
        
        self.current_product = selected_product
        self.show_product_form()
    
    def delete_product(self):
        """حذف منتج"""
        selected_product = self.get_selected_product()
        if not selected_product:
            messagebox.showwarning("تحذير", "يرجى اختيار منتج للحذف")
            return
        
        result = messagebox.askyesno(
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف المنتج '{selected_product.name}'؟"
        )
        
        if result:
            selected_product.delete()
            self.load_products()
            messagebox.showinfo("نجح", "تم حذف المنتج بنجاح")
    
    def show_product_form(self):
        """عرض نموذج إضافة/تعديل المنتج"""
        # إنشاء نافذة جديدة
        form_window = ctk.CTkToplevel(self.parent)
        form_window.title("إضافة منتج" if not self.current_product else "تعديل منتج")
        form_window.geometry("600x700")
        form_window.transient(self.parent)
        form_window.grab_set()
        
        # إطار رئيسي مع شريط تمرير
        main_frame = ctk.CTkScrollableFrame(form_window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # عنوان النموذج
        title = ctk.CTkLabel(
            main_frame,
            text="إضافة منتج جديد" if not self.current_product else "تعديل المنتج",
            font=("Arial", 18, "bold")
        )
        title.pack(pady=10)
        
        # حقول النموذج
        fields = {}
        
        # كود المنتج
        code_frame = ctk.CTkFrame(main_frame)
        code_frame.pack(fill="x", pady=5)
        
        ctk.CTkLabel(code_frame, text="كود المنتج *", width=120).pack(side="right", padx=5)
        fields['code'] = ctk.CTkEntry(code_frame, width=300)
        fields['code'].pack(side="left", padx=5)
        
        # اسم المنتج
        name_frame = ctk.CTkFrame(main_frame)
        name_frame.pack(fill="x", pady=5)
        
        ctk.CTkLabel(name_frame, text="اسم المنتج *", width=120).pack(side="right", padx=5)
        fields['name'] = ctk.CTkEntry(name_frame, width=300)
        fields['name'].pack(side="left", padx=5)
        
        # التصنيف
        category_frame = ctk.CTkFrame(main_frame)
        category_frame.pack(fill="x", pady=5)
        
        ctk.CTkLabel(category_frame, text="التصنيف", width=120).pack(side="right", padx=5)
        
        categories = Category.get_all()
        category_values = [""] + [cat.name for cat in categories]
        fields['category'] = ctk.CTkComboBox(category_frame, values=category_values, width=300)
        fields['category'].pack(side="left", padx=5)
        
        # الوصف
        desc_frame = ctk.CTkFrame(main_frame)
        desc_frame.pack(fill="x", pady=5)
        
        ctk.CTkLabel(desc_frame, text="الوصف", width=120).pack(side="right", padx=5)
        fields['description'] = ctk.CTkTextbox(desc_frame, width=300, height=60)
        fields['description'].pack(side="left", padx=5)
        
        # وحدة القياس
        unit_frame = ctk.CTkFrame(main_frame)
        unit_frame.pack(fill="x", pady=5)
        
        ctk.CTkLabel(unit_frame, text="وحدة القياس *", width=120).pack(side="right", padx=5)
        unit_values = ["قطعة", "كيلو", "متر", "لتر", "علبة", "كرتون", "طن"]
        fields['unit'] = ctk.CTkComboBox(unit_frame, values=unit_values, width=300)
        fields['unit'].pack(side="left", padx=5)
        
        # الباركود
        barcode_frame = ctk.CTkFrame(main_frame)
        barcode_frame.pack(fill="x", pady=5)
        
        ctk.CTkLabel(barcode_frame, text="الباركود", width=120).pack(side="right", padx=5)
        fields['barcode'] = ctk.CTkEntry(barcode_frame, width=200)
        fields['barcode'].pack(side="left", padx=5)
        
        generate_barcode_btn = ctk.CTkButton(
            barcode_frame,
            text="إنشاء",
            command=lambda: self.generate_barcode_for_product(fields),
            width=80
        )
        generate_barcode_btn.pack(side="left", padx=5)
        
        # الأسعار
        prices_frame = ctk.CTkFrame(main_frame)
        prices_frame.pack(fill="x", pady=5)
        
        ctk.CTkLabel(prices_frame, text="سعر التكلفة", width=120).pack(side="right", padx=5)
        fields['cost_price'] = ctk.CTkEntry(prices_frame, width=140)
        fields['cost_price'].pack(side="right", padx=5)
        
        ctk.CTkLabel(prices_frame, text="سعر البيع", width=120).pack(side="left", padx=5)
        fields['selling_price'] = ctk.CTkEntry(prices_frame, width=140)
        fields['selling_price'].pack(side="left", padx=5)
        
        # الكميات
        quantities_frame = ctk.CTkFrame(main_frame)
        quantities_frame.pack(fill="x", pady=5)
        
        ctk.CTkLabel(quantities_frame, text="الكمية الحالية", width=120).pack(side="right", padx=5)
        fields['current_quantity'] = ctk.CTkEntry(quantities_frame, width=140)
        fields['current_quantity'].pack(side="right", padx=5)
        
        limits_frame = ctk.CTkFrame(main_frame)
        limits_frame.pack(fill="x", pady=5)
        
        ctk.CTkLabel(limits_frame, text="الحد الأدنى", width=120).pack(side="right", padx=5)
        fields['min_quantity'] = ctk.CTkEntry(limits_frame, width=140)
        fields['min_quantity'].pack(side="right", padx=5)
        
        ctk.CTkLabel(limits_frame, text="الحد الأقصى", width=120).pack(side="left", padx=5)
        fields['max_quantity'] = ctk.CTkEntry(limits_frame, width=140)
        fields['max_quantity'].pack(side="left", padx=5)
        
        # الحالة
        status_frame = ctk.CTkFrame(main_frame)
        status_frame.pack(fill="x", pady=5)
        
        ctk.CTkLabel(status_frame, text="الحالة", width=120).pack(side="right", padx=5)
        fields['is_active'] = ctk.CTkCheckBox(status_frame, text="نشط")
        fields['is_active'].pack(side="left", padx=5)
        
        # ملء البيانات في حالة التعديل
        if self.current_product:
            self.fill_product_form(fields)
        else:
            # قيم افتراضية للمنتج الجديد
            fields['cost_price'].insert(0, "0")
            fields['selling_price'].insert(0, "0")
            fields['current_quantity'].insert(0, "0")
            fields['min_quantity'].insert(0, "10")
            fields['max_quantity'].insert(0, "1000")
            fields['is_active'].select()
        
        # أزرار الحفظ والإلغاء
        buttons_frame = ctk.CTkFrame(main_frame)
        buttons_frame.pack(fill="x", pady=20)
        
        save_btn = ctk.CTkButton(
            buttons_frame,
            text="💾 حفظ",
            command=lambda: self.save_product(fields, form_window),
            width=120
        )
        save_btn.pack(side="left", padx=10)
        
        cancel_btn = ctk.CTkButton(
            buttons_frame,
            text="❌ إلغاء",
            command=form_window.destroy,
            width=120,
            fg_color="gray"
        )
        cancel_btn.pack(side="right", padx=10)
    
    def fill_product_form(self, fields):
        """ملء نموذج المنتج بالبيانات الحالية"""
        if not self.current_product:
            return
        
        fields['code'].insert(0, self.current_product.code or "")
        fields['name'].insert(0, self.current_product.name or "")
        fields['description'].insert("1.0", self.current_product.description or "")
        fields['unit'].set(self.current_product.unit or "")
        fields['barcode'].insert(0, self.current_product.barcode or "")
        fields['cost_price'].insert(0, str(self.current_product.cost_price))
        fields['selling_price'].insert(0, str(self.current_product.selling_price))
        fields['current_quantity'].insert(0, str(self.current_product.current_quantity))
        fields['min_quantity'].insert(0, str(self.current_product.min_quantity))
        fields['max_quantity'].insert(0, str(self.current_product.max_quantity))
        
        if self.current_product.is_active:
            fields['is_active'].select()
        
        # تعيين التصنيف
        if self.current_product.category_id:
            categories = Category.get_all()
            for cat in categories:
                if cat.id == self.current_product.category_id:
                    fields['category'].set(cat.name)
                    break
    
    def generate_barcode_for_product(self, fields):
        """إنشاء باركود للمنتج"""
        code = fields['code'].get().strip()
        if not code:
            messagebox.showwarning("تحذير", "يرجى إدخال كود المنتج أولاً")
            return
        
        # إنشاء باركود بناءً على كود المنتج
        barcode_value = f"PRD{code}"
        fields['barcode'].delete(0, 'end')
        fields['barcode'].insert(0, barcode_value)
    
    def save_product(self, fields, form_window):
        """حفظ المنتج"""
        try:
            # جمع البيانات من النموذج
            data = {
                'code': fields['code'].get().strip(),
                'name': fields['name'].get().strip(),
                'description': fields['description'].get("1.0", "end-1c").strip(),
                'unit': fields['unit'].get().strip(),
                'barcode': fields['barcode'].get().strip(),
                'cost_price': float(fields['cost_price'].get() or 0),
                'selling_price': float(fields['selling_price'].get() or 0),
                'current_quantity': int(fields['current_quantity'].get() or 0),
                'min_quantity': int(fields['min_quantity'].get() or 0),
                'max_quantity': int(fields['max_quantity'].get() or 1000),
                'is_active': fields['is_active'].get()
            }
            
            # التحقق من صحة البيانات
            errors = ProductValidator.validate_product_data(data)
            if errors:
                messagebox.showerror("خطأ في البيانات", "\n".join(errors))
                return
            
            # البحث عن التصنيف
            category_name = fields['category'].get().strip()
            category_id = None
            if category_name:
                categories = Category.get_all()
                for cat in categories:
                    if cat.name == category_name:
                        category_id = cat.id
                        break
            
            # إنشاء أو تحديث المنتج
            if self.current_product:
                # تحديث منتج موجود
                self.current_product.code = data['code']
                self.current_product.name = data['name']
                self.current_product.description = data['description']
                self.current_product.category_id = category_id
                self.current_product.unit = data['unit']
                self.current_product.barcode = data['barcode']
                self.current_product.cost_price = data['cost_price']
                self.current_product.selling_price = data['selling_price']
                self.current_product.current_quantity = data['current_quantity']
                self.current_product.min_quantity = data['min_quantity']
                self.current_product.max_quantity = data['max_quantity']
                self.current_product.is_active = data['is_active']
                
                self.current_product.save()
                messagebox.showinfo("نجح", "تم تحديث المنتج بنجاح")
            else:
                # إضافة منتج جديد
                new_product = Product(
                    code=data['code'],
                    name=data['name'],
                    description=data['description'],
                    category_id=category_id,
                    unit=data['unit'],
                    barcode=data['barcode'],
                    cost_price=data['cost_price'],
                    selling_price=data['selling_price'],
                    current_quantity=data['current_quantity'],
                    min_quantity=data['min_quantity'],
                    max_quantity=data['max_quantity'],
                    is_active=data['is_active']
                )
                
                new_product.save()
                messagebox.showinfo("نجح", "تم إضافة المنتج بنجاح")
            
            # إغلاق النموذج وتحديث الجدول
            form_window.destroy()
            self.load_products()
            
        except ValueError as e:
            messagebox.showerror("خطأ", "يرجى التأكد من صحة البيانات المدخلة")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ المنتج:\n{str(e)}")
