from config.database import db_manager

class Supplier:
    def __init__(self, id=None, name=None, contact_person=None, phone=None,
                 email=None, address=None, tax_number=None, is_active=True):
        self.id = id
        self.name = name
        self.contact_person = contact_person
        self.phone = phone
        self.email = email
        self.address = address
        self.tax_number = tax_number
        self.is_active = is_active
    
    def save(self):
        """حفظ المورد في قاعدة البيانات"""
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        if self.id:
            # تحديث مورد موجود
            cursor.execute('''
                UPDATE suppliers SET
                    name = ?, contact_person = ?, phone = ?, email = ?,
                    address = ?, tax_number = ?, is_active = ?
                WHERE id = ?
            ''', (self.name, self.contact_person, self.phone, self.email,
                  self.address, self.tax_number, self.is_active, self.id))
        else:
            # إضافة مورد جديد
            cursor.execute('''
                INSERT INTO suppliers (
                    name, contact_person, phone, email, address, tax_number, is_active
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (self.name, self.contact_person, self.phone, self.email,
                  self.address, self.tax_number, self.is_active))
            
            self.id = cursor.lastrowid
        
        conn.commit()
        conn.close()
        return self.id
    
    def delete(self):
        """حذف المورد (إلغاء تفعيل)"""
        if self.id:
            conn = db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('UPDATE suppliers SET is_active = 0 WHERE id = ?', (self.id,))
            
            conn.commit()
            conn.close()
            self.is_active = False
    
    @staticmethod
    def get_by_id(supplier_id):
        """الحصول على مورد بالمعرف"""
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM suppliers WHERE id = ?', (supplier_id,))
        row = cursor.fetchone()
        
        conn.close()
        
        if row:
            return Supplier(
                id=row['id'], name=row['name'], contact_person=row['contact_person'],
                phone=row['phone'], email=row['email'], address=row['address'],
                tax_number=row['tax_number'], is_active=row['is_active']
            )
        return None
    
    @staticmethod
    def get_all(active_only=True):
        """الحصول على جميع الموردين"""
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        if active_only:
            cursor.execute('SELECT * FROM suppliers WHERE is_active = 1 ORDER BY name')
        else:
            cursor.execute('SELECT * FROM suppliers ORDER BY name')
        
        rows = cursor.fetchall()
        conn.close()
        
        suppliers = []
        for row in rows:
            suppliers.append(Supplier(
                id=row['id'], name=row['name'], contact_person=row['contact_person'],
                phone=row['phone'], email=row['email'], address=row['address'],
                tax_number=row['tax_number'], is_active=row['is_active']
            ))
        
        return suppliers
    
    @staticmethod
    def search(query):
        """البحث في الموردين"""
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT * FROM suppliers 
            WHERE is_active = 1 AND (
                name LIKE ? OR contact_person LIKE ? OR phone LIKE ? OR email LIKE ?
            )
            ORDER BY name
        ''', (f'%{query}%', f'%{query}%', f'%{query}%', f'%{query}%'))
        
        rows = cursor.fetchall()
        conn.close()
        
        suppliers = []
        for row in rows:
            suppliers.append(Supplier(
                id=row['id'], name=row['name'], contact_person=row['contact_person'],
                phone=row['phone'], email=row['email'], address=row['address'],
                tax_number=row['tax_number'], is_active=row['is_active']
            ))
        
        return suppliers
