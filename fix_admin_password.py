#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح كلمة مرور المدير الافتراضي
"""

import sys
import os
import hashlib

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def fix_admin_password():
    """إصلاح كلمة مرور المدير"""
    try:
        from config.database import db_manager
        
        # تشفير كلمة المرور الصحيحة
        password = "admin123"
        hashed_password = hashlib.sha256(password.encode()).hexdigest()
        
        # الاتصال بقاعدة البيانات
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        # تحديث كلمة مرور المدير
        cursor.execute('''
            UPDATE users 
            SET password = ? 
            WHERE username = 'admin'
        ''', (hashed_password,))
        
        # التأكد من وجود المستخدم
        cursor.execute('SELECT * FROM users WHERE username = ?', ('admin',))
        user = cursor.fetchone()
        
        if user:
            print("✅ تم تحديث كلمة مرور المدير بنجاح!")
            print(f"اسم المستخدم: admin")
            print(f"كلمة المرور: admin123")
        else:
            # إنشاء المستخدم إذا لم يكن موجوداً
            cursor.execute('''
                INSERT INTO users (username, password, full_name, role, is_active)
                VALUES (?, ?, ?, ?, ?)
            ''', ('admin', hashed_password, 'مدير النظام', 'admin', 1))
            
            print("✅ تم إنشاء المستخدم الافتراضي بنجاح!")
            print(f"اسم المستخدم: admin")
            print(f"كلمة المرور: admin123")
        
        conn.commit()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ حدث خطأ: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("إصلاح كلمة مرور المدير الافتراضي...")
    print("=" * 50)
    
    if fix_admin_password():
        print("\n" + "=" * 50)
        print("✅ تم إصلاح المشكلة بنجاح!")
        print("\nيمكنك الآن تسجيل الدخول باستخدام:")
        print("اسم المستخدم: admin")
        print("كلمة المرور: admin123")
        print("\nاضغط Enter للمتابعة...")
        input()
    else:
        print("\n❌ فشل في إصلاح المشكلة")
        print("يرجى التأكد من وجود قاعدة البيانات")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
