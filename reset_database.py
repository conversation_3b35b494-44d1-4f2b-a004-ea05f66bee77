#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعادة تعيين قاعدة البيانات وإنشاء البيانات التجريبية
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def reset_database():
    """إعادة تعيين قاعدة البيانات"""
    from config.database import db_manager
    
    # حذف ملف قاعدة البيانات إذا كان موجوداً
    if os.path.exists(db_manager.db_path):
        os.remove(db_manager.db_path)
        print("تم حذف قاعدة البيانات القديمة")
    
    # إعادة إنشاء قاعدة البيانات
    db_manager.init_database()
    print("تم إنشاء قاعدة بيانات جديدة")

def main():
    """الدالة الرئيسية"""
    print("إعادة تعيين قاعدة البيانات...")
    print("=" * 50)
    
    try:
        # إعادة تعيين قاعدة البيانات
        reset_database()
        
        # إنشاء البيانات التجريبية
        print("\nإنشاء البيانات التجريبية...")
        from create_sample_data import main as create_data
        create_data()
        
    except Exception as e:
        print(f"حدث خطأ: {e}")

if __name__ == "__main__":
    main()
