# نظام إدارة المخازن 📦

نظام شامل لإدارة المخازن والمنتجات والموردين مطور بلغة Python مع واجهة مستخدم حديثة ومرنة.

## حالة المشروع ✅

**الإصدار الحالي: 1.0.0 - مُكتمل بالكامل**

تم إنجاز جميع الوحدات الأساسية المطلوبة وهي جاهزة للاستخدام الفوري.

## الميزات المُنجزة ✅

### 📦 1. إدارة الأصناف (المنتجات) - مُكتمل
- ✅ تصنيف المنتجات (أثاث، خامات، ملحقات...)
- ✅ إضافة/تعديل/حذف صنف مع واجهة شاملة
- ✅ كود الصنف/الباركود مع إمكانية الإنشاء التلقائي
- ✅ وصف المنتج وإدارة الصور
- ✅ كمية الحد الأدنى/الأقصى للمخزون
- ✅ البحث والتصفية المتقدمة
- ✅ التحقق من صحة البيانات

### 🧾 2. إدارة المخزون - مُكتمل
- ✅ عرض حالة المخزون الحالية مع الألوان التمييزية
- ✅ إضافة/صرف/تعديل المخزون بواجهات مخصصة
- ✅ تنبيهات المخزون المنخفض والنافد
- ✅ فلترة حسب حالة المخزون (عادي، منخفض، نافد)
- ✅ عرض تفاصيل المنتجات مع تاريخ المعاملات
- ✅ إحصائيات شاملة وقيمة المخزون

### 🔁 3. حركات المخزون - مُكتمل
- ✅ سجل شامل للحركات (وارد/صادر/تعديل)
- ✅ البحث المتقدم حسب المنتج أو المورد أو المستخدم
- ✅ فلترة حسب النوع والفترة الزمنية
- ✅ تصدير تقارير الحركات إلى Excel
- ✅ عرض تفاصيل كاملة لكل معاملة
- ✅ إحصائيات فورية للمعاملات

### 🧑‍💼 4. إدارة الموردين - مُكتمل
- ✅ إضافة/تعديل/حذف الموردين
- ✅ بيانات الاتصال الكاملة (هاتف، إيميل، عنوان)
- ✅ الرقم الضريبي مع التحقق من الصحة
- ✅ البحث السريع في الموردين
- ✅ إدارة حالة المورد (نشط/غير نشط)

### 👥 5. المستخدمين والصلاحيات - مُكتمل
- ✅ إدارة شاملة للمستخدمين (للمدير فقط)
- ✅ ثلاثة مستويات صلاحيات (مدير، مشرف، موظف)
- ✅ تشفير آمن لكلمات المرور
- ✅ إعادة تعيين كلمات المرور
- ✅ تتبع العمليات حسب المستخدم
- ✅ حماية المدير الرئيسي من الحذف

### 📊 6. التقارير والإحصائيات - مُكتمل
- ✅ تقرير المخزون الحالي مع القيم
- ✅ تقرير حركات المخزون بفلاتر متقدمة
- ✅ تقرير الموردين مع إحصائيات التعاملات
- ✅ تقرير المخزون المنخفض مع الأولويات
- ✅ رسوم بيانية تفاعلية للمعاملات
- ✅ تصدير جميع التقارير إلى Excel
- ✅ لوحة معلومات إحصائية شاملة

### ⚙️ 7. الإعدادات العامة - مُكتمل
- ✅ إعدادات الشركة (اسم، عملة)
- ✅ إعدادات المخزون (حد التنبيه)
- ✅ إعدادات الواجهة (مظهر، حجم الخط)
- ✅ النسخ الاحتياطية (إنشاء/استعادة)
- ✅ معلومات النظام والإحصائيات
- ✅ واجهة تبويبات منظمة

### 🏠 8. الواجهة الرئيسية (Dashboard) - مُكتمل
- ✅ ملخص سريع للمخزون الحالي
- ✅ تنبيهات فورية للمخزون المنخفض
- ✅ آخر المعاملات مع التفاصيل
- ✅ بطاقات إحصائية تفاعلية
- ✅ تنقل سهل بين جميع الوحدات
- ✅ واجهة عربية كاملة

## التقنيات المستخدمة 🛠️

- **Python 3.8+** - لغة البرمجة الأساسية
- **CustomTkinter** - واجهة المستخدم الحديثة والمرنة
- **SQLite** - قاعدة البيانات المحلية السريعة
- **Matplotlib** - الرسوم البيانية والإحصائيات (اختياري)
- **Pillow** - معالجة الصور (اختياري)
- **OpenpyXL** - تصدير البيانات إلى Excel (اختياري)

## التثبيت والتشغيل السريع 🚀

### 1. التثبيت التلقائي (Windows)
```bash
# تشغيل ملف الإعداد
setup.bat
```

### 2. التثبيت اليدوي
```bash
# تثبيت المكتبة الأساسية
pip install customtkinter

# تشغيل البرنامج
python main.py
```

### 3. إنشاء البيانات التجريبية
```bash
# إعادة تعيين قاعدة البيانات وإنشاء بيانات تجريبية
python reset_database.py
```

## بيانات تسجيل الدخول 🔐

### المدير العام
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123
- **الصلاحيات:** جميع الصلاحيات

### مشرف المخزن
- **اسم المستخدم:** manager1
- **كلمة المرور:** manager123
- **الصلاحيات:** إدارة المنتجات والموردين والتقارير

### موظف المخزن
- **اسم المستخدم:** employee1
- **كلمة المرور:** emp123
- **الصلاحيات:** عرض المخزون وإضافة المعاملات

## البيانات التجريبية المتوفرة 📋

- **6 تصنيفات** (أثاث مكتبي، أجهزة كمبيوتر، قرطاسية، معدات شبكات، أثاث منزلي، إلكترونيات)
- **8 منتجات** متنوعة مع بيانات كاملة
- **3 موردين** مع بيانات اتصال شاملة
- **3 مستخدمين** بصلاحيات مختلفة

## الميزات البارزة 🌟

### الأمان والحماية
- تشفير كلمات المرور
- نظام صلاحيات متدرج
- حماية العمليات الحساسة
- نسخ احتياطية آمنة

### سهولة الاستخدام
- واجهة عربية كاملة
- تصميم حديث ومرن
- تنقل سهل وسريع
- رسائل تأكيد واضحة

### التقارير والتحليل
- تقارير شاملة ومفصلة
- رسوم بيانية تفاعلية
- تصدير متعدد الصيغ
- إحصائيات فورية

### المرونة والتوسع
- قاعدة بيانات قابلة للتوسع
- إعدادات قابلة للتخصيص
- نظام نسخ احتياطي
- كود منظم وقابل للصيانة

## هيكل المشروع 📁

```
warehouse_management/
├── main.py                 # نقطة البداية
├── requirements.txt        # المتطلبات الأساسية
├── requirements-full.txt   # المتطلبات الكاملة
├── setup.bat              # إعداد تلقائي
├── run.bat                # تشغيل سريع
├── config/                # إعدادات النظام
├── models/                # نماذج البيانات
├── views/                 # واجهات المستخدم
├── controllers/           # منطق التحكم
├── utils/                 # الأدوات المساعدة
├── assets/               # الأصول
└── data/                 # قاعدة البيانات
```

## الدعم والمساعدة 🆘

### ملفات مهمة
- `README.md` - دليل شامل
- `QUICK_START.md` - دليل البدء السريع
- `CHANGELOG.md` - سجل التغييرات

### أوامر مفيدة
```bash
python main.py              # تشغيل البرنامج
python reset_database.py    # إعادة تعيين النظام
setup.bat                   # إعداد تلقائي (Windows)
run.bat                     # تشغيل سريع (Windows)
```

## الترخيص 📄

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## المساهمة 🤝

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع للميزة الجديدة
3. إرسال Pull Request

## التطوير المستقبلي 🔮

- [ ] واجهة ويب
- [ ] تطبيق موبايل
- [ ] تكامل مع أنظمة المحاسبة
- [ ] دعم قواعد بيانات متقدمة
- [ ] تقارير متقدمة مع الذكاء الاصطناعي

---

**نظام إدارة المخازن - تم تطويره بالكامل بواسطة Augment Agent** 🤖

**حالة المشروع: مُكتمل وجاهز للاستخدام الفوري** ✅
