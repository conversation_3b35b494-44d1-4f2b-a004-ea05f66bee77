#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء بيانات تجريبية لنظام إدارة المخازن
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from models.product import Product, Category
from models.supplier import Supplier
from models.user import User
from models.transaction import InventoryTransaction

def create_sample_categories():
    """إنشاء تصنيفات تجريبية"""
    categories = [
        ("أثاث مكتبي", "مكاتب وكراسي ومعدات مكتبية"),
        ("أجهزة كمبيوتر", "أجهزة كمبيوتر ولابتوب وملحقاتها"),
        ("قرطاسية", "أدوات كتابة وورق ومستلزمات مكتبية"),
        ("معدات شبكات", "راوترات وكابلات وأجهزة شبكات"),
        ("أثاث منزلي", "أثاث للمنازل والشقق"),
        ("إلكترونيات", "أجهزة إلكترونية متنوعة")
    ]
    
    created_categories = []
    for name, desc in categories:
        category = Category(name=name, description=desc)
        category.save()
        created_categories.append(category)
        print(f"تم إنشاء التصنيف: {name}")
    
    return created_categories

def create_sample_suppliers():
    """إنشاء موردين تجريبيين"""
    suppliers_data = [
        {
            "name": "شركة التقنية المتقدمة",
            "contact_person": "أحمد محمد",
            "phone": "0501234567",
            "email": "<EMAIL>",
            "address": "الرياض، المملكة العربية السعودية",
            "tax_number": "123456789012345"
        },
        {
            "name": "مؤسسة الأثاث الحديث",
            "contact_person": "فاطمة أحمد",
            "phone": "0509876543",
            "email": "<EMAIL>",
            "address": "جدة، المملكة العربية السعودية",
            "tax_number": "987654321098765"
        },
        {
            "name": "شركة القرطاسية الذكية",
            "contact_person": "محمد علي",
            "phone": "0505555555",
            "email": "<EMAIL>",
            "address": "الدمام، المملكة العربية السعودية",
            "tax_number": "555666777888999"
        }
    ]
    
    created_suppliers = []
    for supplier_data in suppliers_data:
        supplier = Supplier(**supplier_data)
        supplier.save()
        created_suppliers.append(supplier)
        print(f"تم إنشاء المورد: {supplier.name}")
    
    return created_suppliers

def create_sample_products(categories):
    """إنشاء منتجات تجريبية"""
    products_data = [
        # أثاث مكتبي
        {
            "code": "DESK001",
            "name": "مكتب مدير خشبي",
            "description": "مكتب مدير من الخشب الطبيعي مع أدراج",
            "category_id": categories[0].id,
            "unit": "قطعة",
            "cost_price": 800.00,
            "selling_price": 1200.00,
            "min_quantity": 5,
            "max_quantity": 50,
            "current_quantity": 15,
            "barcode": "1234567890123"
        },
        {
            "code": "CHAIR001",
            "name": "كرسي مكتب دوار",
            "description": "كرسي مكتب مريح مع مساند للذراعين",
            "category_id": categories[0].id,
            "unit": "قطعة",
            "cost_price": 200.00,
            "selling_price": 350.00,
            "min_quantity": 10,
            "max_quantity": 100,
            "current_quantity": 25,
            "barcode": "1234567890124"
        },
        # أجهزة كمبيوتر
        {
            "code": "LAP001",
            "name": "لابتوب ديل",
            "description": "لابتوب ديل انسبايرون 15 بوصة",
            "category_id": categories[1].id,
            "unit": "قطعة",
            "cost_price": 2500.00,
            "selling_price": 3200.00,
            "min_quantity": 3,
            "max_quantity": 20,
            "current_quantity": 8,
            "barcode": "1234567890125"
        },
        {
            "code": "MOUSE001",
            "name": "ماوس لاسلكي",
            "description": "ماوس لاسلكي بتقنية البلوتوث",
            "category_id": categories[1].id,
            "unit": "قطعة",
            "cost_price": 25.00,
            "selling_price": 45.00,
            "min_quantity": 20,
            "max_quantity": 200,
            "current_quantity": 50,
            "barcode": "1234567890126"
        },
        # قرطاسية
        {
            "code": "PEN001",
            "name": "أقلام جاف زرقاء",
            "description": "علبة أقلام جاف زرقاء 50 قلم",
            "category_id": categories[2].id,
            "unit": "علبة",
            "cost_price": 15.00,
            "selling_price": 25.00,
            "min_quantity": 10,
            "max_quantity": 100,
            "current_quantity": 30,
            "barcode": "1234567890127"
        },
        {
            "code": "PAPER001",
            "name": "ورق A4 أبيض",
            "description": "ورق طباعة A4 أبيض 500 ورقة",
            "category_id": categories[2].id,
            "unit": "رزمة",
            "cost_price": 12.00,
            "selling_price": 20.00,
            "min_quantity": 20,
            "max_quantity": 200,
            "current_quantity": 75,
            "barcode": "1234567890128"
        },
        # معدات شبكات
        {
            "code": "ROUTER001",
            "name": "راوتر واي فاي",
            "description": "راوتر واي فاي عالي السرعة",
            "category_id": categories[3].id,
            "unit": "قطعة",
            "cost_price": 150.00,
            "selling_price": 250.00,
            "min_quantity": 5,
            "max_quantity": 30,
            "current_quantity": 12,
            "barcode": "1234567890129"
        },
        {
            "code": "CABLE001",
            "name": "كابل شبكة",
            "description": "كابل شبكة Cat6 طول 10 متر",
            "category_id": categories[3].id,
            "unit": "قطعة",
            "cost_price": 8.00,
            "selling_price": 15.00,
            "min_quantity": 50,
            "max_quantity": 500,
            "current_quantity": 120,
            "barcode": "1234567890130"
        }
    ]
    
    created_products = []
    for product_data in products_data:
        product = Product(**product_data)
        product.save()
        created_products.append(product)
        print(f"تم إنشاء المنتج: {product.name}")
    
    return created_products

def create_sample_users():
    """إنشاء مستخدمين تجريبيين"""
    users_data = [
        {
            "username": "manager1",
            "password": "manager123",
            "full_name": "سارة أحمد - مشرف المخزن",
            "role": "manager"
        },
        {
            "username": "employee1",
            "password": "emp123",
            "full_name": "خالد محمد - موظف مخزن",
            "role": "employee"
        },
        {
            "username": "employee2",
            "password": "emp123",
            "full_name": "نورا علي - موظفة مخزن",
            "role": "employee"
        }
    ]
    
    created_users = []
    for user_data in users_data:
        user = User(**user_data)
        user.save()
        created_users.append(user)
        print(f"تم إنشاء المستخدم: {user.full_name}")
    
    return created_users

def create_sample_transactions(products, suppliers, users):
    """إنشاء معاملات تجريبية"""
    print("تخطي إنشاء المعاملات لتجنب مشاكل قفل قاعدة البيانات")
    return []

def main():
    """الدالة الرئيسية لإنشاء البيانات التجريبية"""
    print("بدء إنشاء البيانات التجريبية...")
    print("=" * 50)
    
    try:
        # إنشاء التصنيفات
        print("\n1. إنشاء التصنيفات...")
        categories = create_sample_categories()
        
        # إنشاء الموردين
        print("\n2. إنشاء الموردين...")
        suppliers = create_sample_suppliers()
        
        # إنشاء المنتجات
        print("\n3. إنشاء المنتجات...")
        products = create_sample_products(categories)
        
        # إنشاء المستخدمين
        print("\n4. إنشاء المستخدمين...")
        users = create_sample_users()
        
        # إنشاء المعاملات
        print("\n5. إنشاء المعاملات...")
        transactions = create_sample_transactions(products, suppliers, users)
        
        print("\n" + "=" * 50)
        print("تم إنشاء البيانات التجريبية بنجاح!")
        print(f"- {len(categories)} تصنيف")
        print(f"- {len(suppliers)} مورد")
        print(f"- {len(products)} منتج")
        print(f"- {len(users)} مستخدم")
        print(f"- {len(transactions)} معاملة")
        print("\nيمكنك الآن تشغيل البرنامج واستخدام البيانات التجريبية")
        print("بيانات تسجيل الدخول:")
        print("- المدير: admin / admin123")
        print("- المشرف: manager1 / manager123")
        print("- الموظف: employee1 / emp123")
        
    except Exception as e:
        print(f"حدث خطأ أثناء إنشاء البيانات: {e}")

if __name__ == "__main__":
    main()
