@echo off
chcp 65001 >nul
echo تشغيل نظام إدارة المخازن...
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت على النظام
    echo يرجى تثبيت Python 3.8 أو أحدث من https://python.org
    pause
    exit /b 1
)

echo بيانات تسجيل الدخول:
echo - المدير: admin / admin123
echo - المشرف: manager1 / manager123
echo - الموظف: employee1 / emp123
echo.

REM تشغيل البرنامج
python main.py

REM في حالة حدوث خطأ
if errorlevel 1 (
    echo.
    echo حدث خطأ أثناء تشغيل البرنامج
    echo يرجى التأكد من تثبيت المتطلبات: pip install customtkinter
    echo أو تشغيل setup.bat للإعداد التلقائي
    pause
)
