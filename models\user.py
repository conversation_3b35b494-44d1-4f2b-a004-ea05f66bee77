from config.database import db_manager
import hashlib

class User:
    def __init__(self, id=None, username=None, password=None, full_name=None,
                 role='employee', is_active=True):
        self.id = id
        self.username = username
        self.password = password
        self.full_name = full_name
        self.role = role  # 'admin', 'manager', 'employee'
        self.is_active = is_active
    
    def save(self):
        """حفظ المستخدم في قاعدة البيانات"""
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        # تشفير كلمة المرور
        if self.password:
            hashed_password = self.hash_password(self.password)
        else:
            hashed_password = None
        
        if self.id:
            # تحديث مستخدم موجود
            if hashed_password:
                cursor.execute('''
                    UPDATE users SET
                        username = ?, password = ?, full_name = ?, role = ?, is_active = ?
                    WHERE id = ?
                ''', (self.username, hashed_password, self.full_name, self.role, self.is_active, self.id))
            else:
                cursor.execute('''
                    UPDATE users SET
                        username = ?, full_name = ?, role = ?, is_active = ?
                    WHERE id = ?
                ''', (self.username, self.full_name, self.role, self.is_active, self.id))
        else:
            # إضافة مستخدم جديد
            cursor.execute('''
                INSERT INTO users (username, password, full_name, role, is_active)
                VALUES (?, ?, ?, ?, ?)
            ''', (self.username, hashed_password, self.full_name, self.role, self.is_active))
            
            self.id = cursor.lastrowid
        
        conn.commit()
        conn.close()
        return self.id
    
    def delete(self):
        """حذف المستخدم (إلغاء تفعيل)"""
        if self.id:
            conn = db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('UPDATE users SET is_active = 0 WHERE id = ?', (self.id,))
            
            conn.commit()
            conn.close()
            self.is_active = False
    
    @staticmethod
    def hash_password(password):
        """تشفير كلمة المرور"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    @staticmethod
    def authenticate(username, password):
        """التحقق من صحة بيانات المستخدم"""
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        hashed_password = User.hash_password(password)
        cursor.execute('''
            SELECT * FROM users 
            WHERE username = ? AND password = ? AND is_active = 1
        ''', (username, hashed_password))
        
        row = cursor.fetchone()
        conn.close()
        
        if row:
            return User(
                id=row['id'], username=row['username'], password=None,
                full_name=row['full_name'], role=row['role'], is_active=row['is_active']
            )
        return None
    
    @staticmethod
    def get_by_id(user_id):
        """الحصول على مستخدم بالمعرف"""
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM users WHERE id = ?', (user_id,))
        row = cursor.fetchone()
        
        conn.close()
        
        if row:
            return User(
                id=row['id'], username=row['username'], password=None,
                full_name=row['full_name'], role=row['role'], is_active=row['is_active']
            )
        return None
    
    @staticmethod
    def get_all(active_only=True):
        """الحصول على جميع المستخدمين"""
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        if active_only:
            cursor.execute('SELECT * FROM users WHERE is_active = 1 ORDER BY full_name')
        else:
            cursor.execute('SELECT * FROM users ORDER BY full_name')
        
        rows = cursor.fetchall()
        conn.close()
        
        users = []
        for row in rows:
            users.append(User(
                id=row['id'], username=row['username'], password=None,
                full_name=row['full_name'], role=row['role'], is_active=row['is_active']
            ))
        
        return users
    
    def has_permission(self, permission):
        """التحقق من صلاحية المستخدم"""
        permissions = {
            'admin': ['all'],
            'manager': ['view_reports', 'manage_inventory', 'manage_products', 'manage_suppliers'],
            'employee': ['view_inventory', 'add_transaction']
        }
        
        user_permissions = permissions.get(self.role, [])
        return 'all' in user_permissions or permission in user_permissions
